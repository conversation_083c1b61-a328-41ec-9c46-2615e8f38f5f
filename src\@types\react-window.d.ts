/**
 * Ambient type declarations for react-window
 * This provides basic types when @types/react-window is not sufficient
 */

declare module 'react-window' {
  import { Component, ComponentType, ReactElement, CSSProperties, Ref } from 'react';

  export type Direction = 'ltr' | 'rtl';
  export type ScrollDirection = 'forward' | 'backward';
  export type Layout = 'vertical' | 'horizontal';

  export interface ListOnItemsRenderedProps {
    overscanStartIndex: number;
    overscanStopIndex: number;
    visibleStartIndex: number;
    visibleStopIndex: number;
  }

  export interface ListOnScrollProps {
    scrollDirection: ScrollDirection;
    scrollOffset: number;
    scrollUpdateWasRequested: boolean;
  }

  export interface FixedSizeListProps {
    children: ComponentType<ListChildComponentProps>;
    className?: string;
    direction?: Direction;
    height: number | string;
    initialScrollOffset?: number;
    innerRef?: Ref<any>;
    innerElementType?: string | ComponentType<any>;
    itemCount: number;
    itemData?: any;
    itemKey?: (index: number, data: any) => any;
    itemSize: number;
    layout?: Layout;
    onItemsRendered?: (props: ListOnItemsRenderedProps) => any;
    onScroll?: (props: ListOnScrollProps) => any;
    outerRef?: Ref<any>;
    outerElementType?: string | ComponentType<any>;
    overscanCount?: number;
    style?: CSSProperties;
    useIsScrolling?: boolean;
    width: number | string;
  }

  export interface VariableSizeListProps {
    children: ComponentType<ListChildComponentProps>;
    className?: string;
    direction?: Direction;
    estimatedItemSize?: number;
    height: number | string;
    initialScrollOffset?: number;
    innerRef?: Ref<any>;
    innerElementType?: string | ComponentType<any>;
    itemCount: number;
    itemData?: any;
    itemKey?: (index: number, data: any) => any;
    itemSize: (index: number) => number;
    layout?: Layout;
    onItemsRendered?: (props: ListOnItemsRenderedProps) => any;
    onScroll?: (props: ListOnScrollProps) => any;
    outerRef?: Ref<any>;
    outerElementType?: string | ComponentType<any>;
    overscanCount?: number;
    style?: CSSProperties;
    useIsScrolling?: boolean;
    width: number | string;
  }

  export interface GridOnItemsRenderedProps {
    overscanColumnStartIndex: number;
    overscanColumnStopIndex: number;
    overscanRowStartIndex: number;
    overscanRowStopIndex: number;
    visibleColumnStartIndex: number;
    visibleColumnStopIndex: number;
    visibleRowStartIndex: number;
    visibleRowStopIndex: number;
  }

  export interface GridOnScrollProps {
    horizontalScrollDirection: ScrollDirection;
    scrollLeft: number;
    scrollTop: number;
    scrollUpdateWasRequested: boolean;
    verticalScrollDirection: ScrollDirection;
  }

  export interface FixedSizeGridProps {
    children: ComponentType<GridChildComponentProps>;
    className?: string;
    columnCount: number;
    columnWidth: number;
    direction?: Direction;
    height: number | string;
    initialScrollLeft?: number;
    initialScrollTop?: number;
    innerRef?: Ref<any>;
    innerElementType?: string | ComponentType<any>;
    itemData?: any;
    itemKey?: (params: GridItemKeyParams) => any;
    onItemsRendered?: (props: GridOnItemsRenderedProps) => any;
    onScroll?: (props: GridOnScrollProps) => any;
    outerRef?: Ref<any>;
    outerElementType?: string | ComponentType<any>;
    overscanColumnsCount?: number;
    overscanCount?: number;
    overscanRowsCount?: number;
    rowCount: number;
    rowHeight: number;
    style?: CSSProperties;
    useIsScrolling?: boolean;
    width: number | string;
  }

  export interface VariableSizeGridProps {
    children: ComponentType<GridChildComponentProps>;
    className?: string;
    columnCount: number;
    columnWidth: (index: number) => number;
    direction?: Direction;
    estimatedColumnWidth?: number;
    estimatedRowHeight?: number;
    height: number | string;
    initialScrollLeft?: number;
    initialScrollTop?: number;
    innerRef?: Ref<any>;
    innerElementType?: string | ComponentType<any>;
    itemData?: any;
    itemKey?: (params: GridItemKeyParams) => any;
    onItemsRendered?: (props: GridOnItemsRenderedProps) => any;
    onScroll?: (props: GridOnScrollProps) => any;
    outerRef?: Ref<any>;
    outerElementType?: string | ComponentType<any>;
    overscanColumnsCount?: number;
    overscanCount?: number;
    overscanRowsCount?: number;
    rowCount: number;
    rowHeight: (index: number) => number;
    style?: CSSProperties;
    useIsScrolling?: boolean;
    width: number | string;
  }

  export interface ListChildComponentProps {
    index: number;
    style: CSSProperties;
    data?: any;
    isScrolling?: boolean;
  }

  export interface GridChildComponentProps {
    columnIndex: number;
    rowIndex: number;
    style: CSSProperties;
    data?: any;
    isScrolling?: boolean;
  }

  export interface GridItemKeyParams {
    columnIndex: number;
    rowIndex: number;
    data: any;
  }

  export class FixedSizeList extends Component<FixedSizeListProps> {
    scrollTo(scrollOffset: number): void;
    scrollToItem(index: number, align?: 'auto' | 'smart' | 'center' | 'end' | 'start'): void;
  }

  export class VariableSizeList extends Component<VariableSizeListProps> {
    resetAfterIndex(index: number, shouldForceUpdate?: boolean): void;
    scrollTo(scrollOffset: number): void;
    scrollToItem(index: number, align?: 'auto' | 'smart' | 'center' | 'end' | 'start'): void;
  }

  export class FixedSizeGrid extends Component<FixedSizeGridProps> {
    scrollTo(params: { scrollLeft: number; scrollTop: number }): void;
    scrollToItem(params: {
      columnIndex?: number;
      rowIndex?: number;
      align?: 'auto' | 'smart' | 'center' | 'end' | 'start';
    }): void;
  }

  export class VariableSizeGrid extends Component<VariableSizeGridProps> {
    resetAfterColumnIndex(columnIndex: number, shouldForceUpdate?: boolean): void;
    resetAfterIndices(params: {
      columnIndex: number;
      rowIndex: number;
      shouldForceUpdate?: boolean;
    }): void;
    resetAfterRowIndex(rowIndex: number, shouldForceUpdate?: boolean): void;
    scrollTo(params: { scrollLeft: number; scrollTop: number }): void;
    scrollToItem(params: {
      columnIndex?: number;
      rowIndex?: number;
      align?: 'auto' | 'smart' | 'center' | 'end' | 'start';
    }): void;
  }

  export function areEqual(
    prevProps: Record<string, unknown>,
    nextProps: Record<string, unknown>
  ): boolean;

  export function shouldComponentUpdate(
    this: any,
    nextProps: Record<string, unknown>,
    nextState: Record<string, unknown>
  ): boolean;
}
