import * as React from 'react';
import { Outlet } from 'react-router-dom';
import { cn } from '@/utils/cn';

interface StudioLayoutProps {
  children?: React.ReactNode;
  className?: string;
}

/**
 * Studio layout component for creator studio pages
 * Provides a specialized layout for content creation and management
 */
const StudioLayout: React.FC<StudioLayoutProps> = ({ children, className }) => {
  return (
    <div className={cn('min-h-screen bg-gray-50 dark:bg-gray-900', className)}>
      {/* Studio Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                YouTube Studio
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {/* Studio navigation items can be added here */}
            </div>
          </div>
        </div>
      </header>

      {/* Studio Sidebar */}
      <div className="flex">
        <aside className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 min-h-screen">
          <nav className="p-4">
            <ul className="space-y-2">
              <li>
                <a
                  href="/studio/dashboard"
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Dashboard
                </a>
              </li>
              <li>
                <a
                  href="/studio/videos"
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Videos
                </a>
              </li>
              <li>
                <a
                  href="/studio/analytics"
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Analytics
                </a>
              </li>
              <li>
                <a
                  href="/studio/comments"
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Comments
                </a>
              </li>
            </ul>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {children || <Outlet />}
        </main>
      </div>
    </div>
  );
};

export default StudioLayout;