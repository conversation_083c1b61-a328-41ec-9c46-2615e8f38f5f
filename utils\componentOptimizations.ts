import React from 'react';

export function withMemo<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  areEqual?: (prevProps: T, nextProps: T) => boolean
): React.MemoExoticComponent<React.ComponentType<T>> {
  return React.memo(Component, areEqual);
}

export function withErrorBoundary<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>
): React.ComponentType<T> {
  return function WithErrorBoundaryComponent(props: T) {
    const [error, setError] = React.useState<Error | null>(null);

    React.useEffect(() => {
      if (error) {
        console.error('Component error caught by error boundary:', error);
      }
    }, [error]);

    const resetError = React.useCallback(() => {
      setError(null);
    }, []);

    if (error) {
      if (fallback) {
        const FallbackComponent = fallback;
        return <FallbackComponent error={error} resetError={resetError} />;
      }
      return (
        <div className="error-boundary p-4 bg-red-50 border border-red-200 rounded-md">
          <h3 className="text-red-800 font-medium mb-2">Something went wrong</h3>
          <p className="text-red-600 text-sm mb-3">{error.message}</p>
          <button 
            onClick={resetError}
            className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
          >
            Try again
          </button>
        </div>
      );
    }

    try {
      return <Component {...props} />;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      return null;
    }
  };
}

export function withSuspense<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  fallback?: React.ReactNode
): React.ComponentType<T> {
  return function WithSuspenseComponent(props: T) {
    return (
      <React.Suspense fallback={fallback || <div>Loading...</div>}>
        <Component {...props} />
      </React.Suspense>
    );
  };
}
