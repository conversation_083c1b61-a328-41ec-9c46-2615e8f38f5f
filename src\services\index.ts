/**
 * Centralized Services Index
 * Export all services for easy importing
 */

// Core API services
export { api } from './api/base';
export { youtubeService } from './api/youtubeService';

// Business logic services
export { searchService } from './searchService';
export { analyticsService } from './analyticsService';
export { uploadService } from './uploadService';
export { unifiedDataService } from './unifiedDataService';

// Adapters
export { unifiedVideoToVideo, unifiedVideosToVideos } from './adapters/videoAdapter';

// Types
export type { SearchFilters, SearchSuggestion, SearchResult } from './searchService';
export type { AnalyticsEvent, UserSession, AnalyticsConfig } from './analyticsService';
export type { UploadResponse } from './uploadService';
