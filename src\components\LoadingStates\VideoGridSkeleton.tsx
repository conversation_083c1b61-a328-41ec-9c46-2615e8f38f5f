import * as React from 'react';

interface VideoGridSkeletonProps {
  count?: number;
}

const VideoGridSkeleton: React.FC<VideoGridSkeletonProps> = ({ count = 18 }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="animate-pulse">
          {/* Video thumbnail skeleton */}
          <div className="aspect-video bg-gray-300 dark:bg-gray-700 rounded-lg mb-3"></div>
          
          {/* Video info skeleton */}
          <div className="flex gap-3">
            {/* Channel avatar skeleton */}
            <div className="w-9 h-9 bg-gray-300 dark:bg-gray-700 rounded-full flex-shrink-0"></div>
            
            <div className="flex-1 min-w-0">
              {/* Title skeleton */}
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
              
              {/* Channel name skeleton */}
              <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-1/2 mb-1"></div>
              
              {/* Views and date skeleton */}
              <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default VideoGridSkeleton;