import type { Video, User } from '@/types';

// Re-export core types
export type { Video, User };

// User-specific extended types
export interface UserHistory {
  videos: Array<Video & { 
    watchedAt: string; 
    watchProgress: number;
    watchDuration: number;
  }>;
  totalCount: number;
  lastUpdated: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  autoplay: boolean;
  notifications: {
    email: boolean;
    push: boolean;
    subscriptions: boolean;
    recommendations: boolean;
  };
  privacy: {
    showHistory: boolean;
    showLikedVideos: boolean;
    showSubscriptions: boolean;
  };
}

export interface UserStats {
  totalWatchTime: number;
  videosWatched: number;
  channelsSubscribed: number;
  playlistsCreated: number;
  videosLiked: number;
  commentsPosted: number;
}

export interface UserSubscription {
  channelId: string;
  channelName: string;
  channelAvatar: string;
  subscribedAt: string;
  notificationsEnabled: boolean;
  isVerified: boolean;
}

export interface UserPlaylistSummary {
  id: string;
  title: string;
  videoCount: number;
  thumbnailUrl: string;
  visibility: 'public' | 'unlisted' | 'private';
  lastUpdated: string;
}

export interface UserActivity {
  type: 'watch' | 'like' | 'comment' | 'subscribe' | 'playlist_create' | 'playlist_add';
  videoId?: string;
  channelId?: string;
  playlistId?: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface UserContextType {
  user: User | null;
  preferences: UserPreferences;
  stats: UserStats;
  subscriptions: UserSubscription[];
  history: UserHistory;
  likedVideos: Video[];
  watchLaterVideos: Video[];
  playlists: UserPlaylistSummary[];
  loading: boolean;
  error: string | null;
  
  // Actions
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>;
  addToHistory: (video: Video, watchProgress: number) => Promise<void>;
  removeFromHistory: (videoId: string) => Promise<void>;
  clearHistory: () => Promise<void>;
  likeVideo: (videoId: string) => Promise<void>;
  unlikeVideo: (videoId: string) => Promise<void>;
  addToWatchLater: (videoId: string) => Promise<void>;
  removeFromWatchLater: (videoId: string) => Promise<void>;
  subscribeToChannel: (channelId: string) => Promise<void>;
  unsubscribeFromChannel: (channelId: string) => Promise<void>;
}
