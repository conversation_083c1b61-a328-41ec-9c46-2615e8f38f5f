import { describe, it, expect, vi, beforeEach } from 'vitest';

import { unifiedDataService } from '../unifiedDataService';

// Mock dependencies to avoid external calls during testing
vi.mock('../api/youtubeService', () => ({
  youtubeService: {
    fetchVideos: vi.fn(),
    fetchChannel: vi.fn(),
    clearCache: vi.fn(),
  },
}));

vi.mock('../metadataNormalizationService', () => ({
  metadataNormalizationService: {
    normalizeLocalVideo: vi.fn(),
    normalizeYouTubeVideo: vi.fn(),
    normalizeLocalChannel: vi.fn(),
    normalizeYouTubeChannel: vi.fn(),
  },
}));

vi.mock('../../../services/mockVideoService', () => ({
  getVideos: vi.fn(),
  getShortsVideos: vi.fn(),
  getVideosByCategory: vi.fn(),
  searchVideos: vi.fn(),
  getVideoById: vi.fn(),
  getChannelById: vi.fn(),
}));

describe('extractYouTubeId', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    unifiedDataService.clearCache();
  });

  // Helper function to access the private extractYouTubeId method
  const getExtractYouTubeId = () => {
    return (unifiedDataService as any).extractYouTubeId.bind(unifiedDataService);
  };

  describe('Google Search Prefixed IDs', () => {
    it('should extract YouTube ID from google-search-prefixed string', () => {
      const extractYouTubeId = getExtractYouTubeId();
      const result = extractYouTubeId('google-search-YQHsXMglC9A');

      expect(result).toBe('YQHsXMglC9A');
    });

    it('should handle various YouTube IDs with google-search prefix', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(extractYouTubeId('google-search-dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ');
      expect(extractYouTubeId('google-search-jNQXAC9IVRw')).toBe('jNQXAC9IVRw');
      expect(extractYouTubeId('google-search-9bZkp7q19f0')).toBe('9bZkp7q19f0');
    });
  });

  describe('YouTube Prefixed IDs', () => {
    it('should extract YouTube ID from youtube-prefixed string', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(extractYouTubeId('youtube-YQHsXMglC9A')).toBe('YQHsXMglC9A');
      expect(extractYouTubeId('youtube-dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ');
    });
  });

  describe('Valid YouTube IDs', () => {
    it('should return valid 11-character YouTube IDs as-is', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(extractYouTubeId('YQHsXMglC9A')).toBe('YQHsXMglC9A');
      expect(extractYouTubeId('dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ');
      expect(extractYouTubeId('jNQXAC9IVRw')).toBe('jNQXAC9IVRw');
    });

    it('should handle YouTube IDs with valid characters (letters, numbers, hyphens, underscores)', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(extractYouTubeId('abc123-_XYZ')).toBe('abc123-_XYZ');
      expect(extractYouTubeId('A1B2C3D4E5F')).toBe('A1B2C3D4E5F');
    });
  });

  describe('Invalid IDs', () => {
    it('should return null for invalid YouTube IDs', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(extractYouTubeId('invalid')).toBeNull(); // Too short
      expect(extractYouTubeId('toolongforayoutubeid')).toBeNull(); // Too long
      expect(extractYouTubeId('invalid@#$%')).toBeNull(); // Invalid characters
      expect(extractYouTubeId('')).toBeNull(); // Empty string
    });

    it('should return null for google-search prefix without ID', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(extractYouTubeId('google-search-')).toBe(''); // Empty after prefix
      expect(extractYouTubeId('google-search')).toBeNull(); // No prefix separator
    });

    it('should return null for youtube prefix without ID', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(extractYouTubeId('youtube-')).toBe(''); // Empty after prefix
      expect(extractYouTubeId('youtube')).toBeNull(); // No prefix separator
    });
  });

  describe('Edge Cases', () => {
    it('should handle null and undefined inputs', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(() => extractYouTubeId(null)).toThrow();
      expect(() => extractYouTubeId(undefined)).toThrow();
    });

    it('should be case sensitive', () => {
      const extractYouTubeId = getExtractYouTubeId();

      expect(extractYouTubeId('GOOGLE-SEARCH-YQHsXMglC9A')).toBeNull(); // Wrong case
      expect(extractYouTubeId('YouTube-YQHsXMglC9A')).toBeNull(); // Wrong case
    });
  });
});
