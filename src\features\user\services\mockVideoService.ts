import type { Video } from '@/types';

// Mock video data for user features
const mockVideos: Video[] = [
  {
    id: 'user_video_1',
    title: 'How to Build a React App',
    description: 'Learn how to build a modern React application from scratch',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    videoUrl: 'https://example.com/video1.mp4',
    duration: '15:30',
    views: '2.1M',
    viewCount: 2100000,
    likes: 45000,
    dislikes: 800,
    commentCount: 1200,
    publishedAt: '2024-01-20T14:00:00Z',
    channelId: 'tech_channel',
    channelName: 'Tech Tutorials',
    channelAvatarUrl: 'https://via.placeholder.com/40x40',
    isVerified: true,
    category: 'Education',
    tags: ['react', 'javascript', 'tutorial', 'programming'],
    createdAt: '2024-01-20T14:00:00Z',
    updatedAt: '2024-01-20T14:00:00Z',
  },
  {
    id: 'user_video_2',
    title: 'JavaScript ES6 Features',
    description: 'Explore the latest JavaScript ES6 features and how to use them',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    videoUrl: 'https://example.com/video2.mp4',
    duration: '12:45',
    views: '1.8M',
    viewCount: 1800000,
    likes: 38000,
    dislikes: 600,
    commentCount: 950,
    publishedAt: '2024-01-18T10:30:00Z',
    channelId: 'tech_channel',
    channelName: 'Tech Tutorials',
    channelAvatarUrl: 'https://via.placeholder.com/40x40',
    isVerified: true,
    category: 'Education',
    tags: ['javascript', 'es6', 'programming', 'web development'],
    createdAt: '2024-01-18T10:30:00Z',
    updatedAt: '2024-01-18T10:30:00Z',
  },
  {
    id: 'user_video_3',
    title: 'CSS Grid Layout Tutorial',
    description: 'Master CSS Grid layout with practical examples',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    videoUrl: 'https://example.com/video3.mp4',
    duration: '18:20',
    views: '1.5M',
    viewCount: 1500000,
    likes: 32000,
    dislikes: 400,
    commentCount: 780,
    publishedAt: '2024-01-15T16:45:00Z',
    channelId: 'design_channel',
    channelName: 'Web Design Pro',
    channelAvatarUrl: 'https://via.placeholder.com/40x40',
    isVerified: false,
    category: 'Education',
    tags: ['css', 'grid', 'layout', 'web design'],
    createdAt: '2024-01-15T16:45:00Z',
    updatedAt: '2024-01-15T16:45:00Z',
  },
];

// Mock user history
const mockHistory: Array<Video & { watchedAt: string; watchProgress: number }> = 
  mockVideos.map((video, index) => ({
    ...video,
    watchedAt: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString(),
    watchProgress: Math.random() * 100,
  }));

// Mock liked videos
const mockLikedVideos: Video[] = mockVideos.slice(0, 2);

// Mock watch later videos
const mockWatchLaterVideos: Video[] = mockVideos.slice(1);

export const getUserHistory = async (): Promise<Array<Video & { watchedAt: string; watchProgress: number }>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  return mockHistory;
};

export const getLikedVideos = async (): Promise<Video[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  return mockLikedVideos;
};

export const getWatchLaterVideos = async (): Promise<Video[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  return mockWatchLaterVideos;
};

export const addToWatchLater = async (videoId: string): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const video = mockVideos.find(v => v.id === videoId);
  if (video && !mockWatchLaterVideos.find(v => v.id === videoId)) {
    mockWatchLaterVideos.push(video);
    return true;
  }
  return false;
};

export const removeFromWatchLater = async (videoId: string): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const index = mockWatchLaterVideos.findIndex(v => v.id === videoId);
  if (index !== -1) {
    mockWatchLaterVideos.splice(index, 1);
    return true;
  }
  return false;
};

export const likeVideo = async (videoId: string): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const video = mockVideos.find(v => v.id === videoId);
  if (video && !mockLikedVideos.find(v => v.id === videoId)) {
    mockLikedVideos.push(video);
    return true;
  }
  return false;
};

export const unlikeVideo = async (videoId: string): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const index = mockLikedVideos.findIndex(v => v.id === videoId);
  if (index !== -1) {
    mockLikedVideos.splice(index, 1);
    return true;
  }
  return false;
};

export const clearHistory = async (): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  mockHistory.length = 0;
  return true;
};

export const removeFromHistory = async (videoId: string): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const index = mockHistory.findIndex(v => v.id === videoId);
  if (index !== -1) {
    mockHistory.splice(index, 1);
    return true;
  }
  return false;
};
