/**
 * Analytics Service Unit Tests
 * Tests for centralized analytics functionality
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';

import { analyticsService } from '../analyticsService';

import type { AnalyticsData } from '../analyticsService';

// Mock the API
vi.mock('../api/base', () => ({
  api: {
    get: vi.fn(),
  },
}));

describe('AnalyticsService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('fetchVideoAnalytics', () => {
    it('should fetch analytics data for a video', async () => {
      const { api } = await import('../api/base');
      const mockAnalytics: AnalyticsData = {
        viewCount: 1000,
        likeCount: 50,
        commentCount: 10,
      };

      vi.mocked(api.get).mockResolvedValue({ data: mockAnalytics });

      const result = await analyticsService.fetchVideoAnalytics('test-video-id');

      expect(result).toEqual(mockAnalytics);
      expect(api.get).toHaveBeenCalledWith('/api/analytics/videos/test-video-id');
    });

    it('should throw error when API call fails', async () => {
      const { api } = await import('../api/base');

      vi.mocked(api.get).mockRejectedValue(new Error('API Error'));

      await expect(analyticsService.fetchVideoAnalytics('test-video-id'))
        .rejects.toThrow('API Error');
    });

    it('should handle different video IDs', async () => {
      const { api } = await import('../api/base');
      const mockAnalytics: AnalyticsData = {
        viewCount: 2000,
        likeCount: 100,
        commentCount: 20,
      };

      vi.mocked(api.get).mockResolvedValue({ data: mockAnalytics });

      const result = await analyticsService.fetchVideoAnalytics('another-video-id');

      expect(result).toEqual(mockAnalytics);
      expect(api.get).toHaveBeenCalledWith('/api/analytics/videos/another-video-id');
    });
  });
});
