import type { Video, Playlist, PlaylistVideo } from '@/types';

// Re-export core types for convenience
export type { Video, Playlist, PlaylistVideo };

// Playlist-specific extended types
export interface PlaylistWithVideos extends Playlist {
  videos: Video[];
  title: string;
  description: string;
  updatedAt: string;
  videoIds?: string[]; // For backward compatibility
}

export interface PlaylistFormData {
  title: string;
  description: string;
  visibility: 'public' | 'unlisted' | 'private';
}

export interface PlaylistStats {
  totalVideos: number;
  totalDuration: number;
  totalViews: number;
  lastUpdated: string;
}

export interface PlaylistFilters {
  sortBy: 'dateAdded' | 'dateCreated' | 'title' | 'duration';
  order: 'asc' | 'desc';
  visibility?: 'public' | 'unlisted' | 'private';
}

export interface PlaylistSearchResult {
  playlists: Playlist[];
  totalCount: number;
  hasMore: boolean;
}

// Playlist management actions
export type PlaylistAction = 
  | { type: 'ADD_VIDEO'; payload: { playlistId: string; videoId: string } }
  | { type: 'REMOVE_VIDEO'; payload: { playlistId: string; videoId: string } }
  | { type: 'REORDER_VIDEOS'; payload: { playlistId: string; videoIds: string[] } }
  | { type: 'UPDATE_PLAYLIST'; payload: { playlistId: string; updates: Partial<Playlist> } }
  | { type: 'DELETE_PLAYLIST'; payload: { playlistId: string } };

// Playlist context types
export interface PlaylistContextType {
  playlists: Playlist[];
  currentPlaylist: PlaylistWithVideos | null;
  loading: boolean;
  error: string | null;
  createPlaylist: (data: PlaylistFormData) => Promise<Playlist>;
  updatePlaylist: (id: string, updates: Partial<Playlist>) => Promise<void>;
  deletePlaylist: (id: string) => Promise<void>;
  addVideoToPlaylist: (playlistId: string, videoId: string) => Promise<void>;
  removeVideoFromPlaylist: (playlistId: string, videoId: string) => Promise<void>;
  getPlaylistById: (id: string) => Promise<PlaylistWithVideos | null>;
  getUserPlaylists: () => Promise<void>;
}
