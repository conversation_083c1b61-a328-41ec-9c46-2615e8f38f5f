import React from 'react';
import { Short } from '../src/types';

export interface ShortDisplayCardProps {
  short: Short;
  onLike?: (shortId: string) => void;
  onComment?: (shortId: string) => void;
  onShare?: (shortId: string) => void;
  onFollow?: (channelName: string) => void;
  className?: string;
}

export default function ShortDisplayCard({
  short,
  onLike,
  onComment,
  onShare,
  onFollow,
  className = ''
}: ShortDisplayCardProps) {
  return (
    <div className={`relative bg-black rounded-lg overflow-hidden aspect-[9/16] max-w-sm ${className}`}>
      {/* Video Container */}
      <div className="relative h-full">
        <video
          src={short.videoUrl || ''}
          poster={short.thumbnailUrl || ''}
          className="w-full h-full object-cover"
          muted
          loop
          autoPlay={false}
        />
        
        {/* Overlay Controls */}
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
          {/* Video Info */}
          <div className="text-white mb-3">
            <h3 className="font-semibold text-sm mb-1 line-clamp-2">
              {short.title || 'Untitled Short'}
            </h3>
            <div className="flex items-center space-x-2 text-xs opacity-90">
              <img
                src={short.channelAvatarUrl || '/default-avatar.jpg'}
                alt={short.channelName || 'Channel'}
                className="w-6 h-6 rounded-full"
              />
              <span>{short.channelName || 'Unknown Channel'}</span>
              <span>•</span>
              <span>{short.views || '0'} views</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Like Button */}
              <button
                onClick={() => onLike?.(short.id)}
                className="flex items-center space-x-1 text-white hover:text-red-500 transition-colors"
                aria-label="Like"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                </svg>
                <span className="text-xs">{short.likes || 0}</span>
              </button>

              {/* Comment Button */}
              <button
                onClick={() => onComment?.(short.id)}
                className="flex items-center space-x-1 text-white hover:text-blue-500 transition-colors"
                aria-label="Comment"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <span className="text-xs">{short.commentCount || 0}</span>
              </button>

              {/* Share Button */}
              <button
                onClick={() => onShare?.(short.id)}
                className="flex items-center space-x-1 text-white hover:text-green-500 transition-colors"
                aria-label="Share"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
              </button>
            </div>

            {/* Follow Button */}
            <button
              onClick={() => onFollow?.(short.channelName || '')}
              className="px-3 py-1 bg-red-600 text-white text-xs rounded-full hover:bg-red-700 transition-colors"
            >
              Follow
            </button>
          </div>
        </div>

        {/* Play/Pause Button */}
        <button 
          className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 hover:opacity-100 transition-opacity"
          aria-label="Play/Pause"
        >
          <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
}
