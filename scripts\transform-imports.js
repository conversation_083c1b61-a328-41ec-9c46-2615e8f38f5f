#!/usr/bin/env node

/**
 * JSCodeshift transform to convert relative imports to path aliases
 * 
 * This transform will convert imports like:
 * - from '../../../types' to '@/types'
 * - from '../../components/Button' to '@/components/Button'
 * - from '../hooks/useData' to '@/hooks/useData'
 */

const path = require('path');

module.exports = function transformer(fileInfo, api) {
  const j = api.jscodeshift;
  const root = j(fileInfo.source);
  
  // Get the directory of the current file relative to src
  const filePath = fileInfo.path.replace(/\\/g, '/');
  const srcIndex = filePath.indexOf('src/');
  
  if (srcIndex === -1) {
    // File is not in src directory, skip transformation
    return fileInfo.source;
  }
  
  const relativePath = filePath.substring(srcIndex);
  const fileDir = path.posix.dirname(relativePath);
  
  // Helper function to resolve relative path to absolute path within src
  function resolveRelativePath(importPath, currentDir) {
    if (!importPath.startsWith('.')) {
      return null; // Not a relative import
    }
    
    // Resolve the path using posix for consistent forward slashes
    const resolved = path.posix.resolve('/', currentDir, importPath);
    return resolved.substring(1); // Remove leading slash
  }
  
  // Helper function to convert absolute src path to alias
  function pathToAlias(srcPath) {
    // Normalize the path
    let normalizedPath = srcPath.replace(/\\/g, '/');
    
    // Remove 'src/src/' duplication if it exists
    if (normalizedPath.startsWith('src/src/')) {
      normalizedPath = normalizedPath.replace('src/src/', 'src/');
    }
    
    if (normalizedPath === 'src') {
      return '@';
    }
    
    // Map specific directories to their aliases
    const aliasMap = {
      'src/components': '@/components',
      'src/features': '@/features',
      'src/hooks': '@/hooks',
      'src/utils': '@/utils',
      'src/services': '@/services',
      'src/store': '@/store',
      'src/types': '@/types',
      'src/styles': '@/styles',
      'src/assets': '@/assets',
      'src/lib': '@/lib',
      'src/contexts': '@/contexts',
      'src/providers': '@/providers',
      'src/config': '@/config'
    };
    
    // Check for exact matches first
    if (aliasMap[normalizedPath]) {
      return aliasMap[normalizedPath];
    }
    
    // Check for subdirectories
    for (const [srcPrefix, alias] of Object.entries(aliasMap)) {
      if (normalizedPath.startsWith(srcPrefix + '/')) {
        return normalizedPath.replace(srcPrefix, alias);
      }
    }
    
    // Default to @/ prefix for anything else under src
    if (normalizedPath.startsWith('src/')) {
      return '@/' + normalizedPath.substring(4);
    }
    
    return '@/' + normalizedPath;
  }
  
  let hasChanges = false;
  
  // Transform import declarations
  root.find(j.ImportDeclaration).forEach(nodePath => {
    const importPath = nodePath.node.source.value;
    
    if (typeof importPath === 'string' && importPath.startsWith('.')) {
      const resolvedPath = resolveRelativePath(importPath, fileDir);
      
      if (resolvedPath) {
        const aliasPath = pathToAlias(resolvedPath);
        
        if (aliasPath && aliasPath !== importPath) {
          console.log(`Transforming: ${importPath} -> ${aliasPath} (in ${filePath})`);
          nodePath.node.source.value = aliasPath;
          hasChanges = true;
        }
      }
    }
  });
  
  // Transform dynamic imports
  root.find(j.CallExpression, {
    callee: { type: 'Import' }
  }).forEach(nodePath => {
    const arg = nodePath.node.arguments[0];
    
    if (arg && arg.type === 'Literal' && typeof arg.value === 'string' && arg.value.startsWith('.')) {
      const importPath = arg.value;
      const resolvedPath = resolveRelativePath(importPath, fileDir);
      
      if (resolvedPath) {
        const aliasPath = pathToAlias(resolvedPath);
        
        if (aliasPath && aliasPath !== importPath) {
          console.log(`Transforming dynamic import: ${importPath} -> ${aliasPath} (in ${filePath})`);
          arg.value = aliasPath;
          hasChanges = true;
        }
      }
    }
  });
  
  // Transform require() calls
  root.find(j.CallExpression, {
    callee: { name: 'require' }
  }).forEach(nodePath => {
    const arg = nodePath.node.arguments[0];
    
    if (arg && arg.type === 'Literal' && typeof arg.value === 'string' && arg.value.startsWith('.')) {
      const importPath = arg.value;
      const resolvedPath = resolveRelativePath(importPath, fileDir);
      
      if (resolvedPath) {
        const aliasPath = pathToAlias(resolvedPath);
        
        if (aliasPath && aliasPath !== importPath) {
          console.log(`Transforming require: ${importPath} -> ${aliasPath} (in ${filePath})`);
          arg.value = aliasPath;
          hasChanges = true;
        }
      }
    }
  });
  
  return hasChanges ? root.toSource({ quote: 'single' }) : fileInfo.source;
};

module.exports.parser = 'tsx';
