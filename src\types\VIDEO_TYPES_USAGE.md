# Unified Video Types - Usage Guide

This document explains how to use the new unified Video type system that consolidates all scattered video type definitions.

## Overview

The unified Video type system provides:

1. **Single source of truth** - All video-related types are defined in one place
2. **External API mapping** - Adapters to normalize external API responses
3. **Backward compatibility** - Legacy field names are supported through aliases
4. **Type safety** - Proper nullability and field validation

## Main Types

### Video Interface

The core `Video` interface in `src/types/Video.ts` contains all possible video fields:

```typescript
import { Video } from '@/types/Video';

// Example usage in a component
interface VideoCardProps {
  video: Video;
  onPlay: (video: Video) => void;
}

function VideoCard({ video, onPlay }: VideoCardProps) {
  return (
    <div>
      <h3>{video.title}</h3>
      <p>{video.channelName}</p>
      <p>{video.views} views</p>
      <img src={video.thumbnailUrl} alt={video.title} />
      <button onClick={() => onPlay(video)}>Play</button>
    </div>
  );
}
```

### External API Types

For handling different API responses:

```typescript
import { ApiYouTubeVideo, ApiExternalVideo, ApiVideo } from '@/types/Video';

// YouTube API response
const youtubeResponse: ApiYouTubeVideo = {
  id: 'abc123',
  snippet: {
    title: 'Video Title',
    description: 'Video description',
    // ... other YouTube API fields
  },
  statistics: {
    viewCount: '1000',
    likeCount: '50',
    // ... other stats
  },
  contentDetails: {
    duration: 'PT4M13S',
    // ... other content details
  }
};

// Generic external API response
const externalResponse: ApiExternalVideo = {
  id: 'xyz789',
  title: 'External Video',
  description: 'Description',
  thumbnail_url: 'https://example.com/thumb.jpg',
  view_count: 2000,
  like_count: 75
};
```

## Normalization

Use the normalization utilities to convert external API responses to the unified Video interface:

```typescript
import { normalizeVideo, normalizeYouTubeVideo, normalizeExternalVideo } from '@/utils/normalizeVideo';

// Automatically detect and normalize any API response
const video = normalizeVideo(apiResponse);

// Explicitly normalize YouTube API response
const youtubeVideo = normalizeYouTubeVideo(youtubeApiResponse);

// Explicitly normalize external API response
const externalVideo = normalizeExternalVideo(externalApiResponse);

// Normalize multiple videos
const videos = normalizeVideos(apiResponses);
```

## Services Usage

In your services, always return the normalized Video type:

```typescript
import { Video } from '@/types/Video';
import { normalizeVideo } from '@/utils/normalizeVideo';

class VideoService {
  async getVideo(id: string): Promise<Video> {
    const response = await fetch(`/api/videos/${id}`);
    const apiVideo = await response.json();
    
    // Always normalize before returning
    return normalizeVideo(apiVideo);
  }

  async getVideos(params: any): Promise<Video[]> {
    const response = await fetch('/api/videos', { 
      method: 'POST', 
      body: JSON.stringify(params) 
    });
    const apiVideos = await response.json();
    
    // Normalize all videos
    return apiVideos.map(normalizeVideo);
  }
}
```

## Component Props

Always use the unified Video type for component props:

```typescript
import { Video } from '@/types/Video';

interface VideoPlayerProps {
  video: Video;
  autoplay?: boolean;
  controls?: boolean;
}

interface VideoListProps {
  videos: Video[];
  loading?: boolean;
  onVideoSelect: (video: Video) => void;
}

interface VideoGridProps {
  videos: Video[];
  columns?: number;
  showChannelInfo?: boolean;
}
```

## Hooks Usage

When creating custom hooks, use the unified types:

```typescript
import { Video } from '@/types/Video';
import { normalizeVideos } from '@/utils/normalizeVideo';

function useVideos(query: string) {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    async function fetchVideos() {
      setLoading(true);
      try {
        const response = await fetch(`/api/search?q=${query}`);
        const apiVideos = await response.json();
        
        // Always normalize
        const normalizedVideos = normalizeVideos(apiVideos.items);
        setVideos(normalizedVideos);
      } catch (error) {
        console.error('Failed to fetch videos:', error);
      } finally {
        setLoading(false);
      }
    }

    if (query) {
      fetchVideos();
    }
  }, [query]);

  return { videos, loading };
}
```

## Type Guards

Use type guards to check API response types:

```typescript
import { isYouTubeVideo, isExternalVideo } from '@/utils/normalizeVideo';

function processApiResponse(apiVideo: unknown) {
  if (isYouTubeVideo(apiVideo)) {
    // TypeScript knows this is ApiYouTubeVideo
    console.log('YouTube video:', apiVideo.snippet.title);
  } else if (isExternalVideo(apiVideo)) {
    // TypeScript knows this is ApiExternalVideo
    console.log('External video:', apiVideo.title);
  }
}
```

## Supporting Types

The system also includes supporting types that can be used independently:

```typescript
import { 
  Channel, 
  Comment, 
  AnalyticsSlice,
  VideoChannel,
  VideoStatistics
} from '@/types/Video';

// Channel info
const channel: Channel = {
  id: 'channel123',
  name: 'My Channel',
  description: 'Channel description',
  avatarUrl: 'https://example.com/avatar.jpg',
  // ... other channel fields
};

// Video statistics
const stats: VideoStatistics = {
  viewCount: 1000,
  likeCount: 50,
  dislikeCount: 5,
  favoriteCount: 25,
  commentCount: 10
};
```

## Migration from Legacy Types

If you're migrating from legacy types, the unified Video interface supports most legacy field names:

```typescript
// Legacy usage (still works)
const viewCount = video.viewCount; // number
const views = video.views; // formatted string like "1.2M views"

// Both channelName and channelTitle work
const name1 = video.channelName;
const name2 = video.channelTitle; // alias

// Both thumbnailUrl and thumbnail work
const thumb1 = video.thumbnailUrl;
const thumb2 = video.thumbnail; // alias
```

## Best Practices

1. **Always normalize external API responses** before using them in your application
2. **Use the unified Video type** for all component props and state
3. **Import types from the main index** for consistency: `import { Video } from '@/types'`
4. **Use type guards** when working with unknown API responses
5. **Leverage TypeScript** for compile-time type checking

## Error Handling

The normalization functions are designed to be robust:

```typescript
import { normalizeVideo } from '@/utils/normalizeVideo';

function safeNormalizeVideo(apiVideo: unknown): Video | null {
  try {
    return normalizeVideo(apiVideo as any);
  } catch (error) {
    console.error('Failed to normalize video:', error);
    return null;
  }
}
```

This unified type system ensures consistency across your entire application while maintaining backward compatibility and providing excellent developer experience.
