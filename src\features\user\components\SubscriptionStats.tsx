import React from 'react';

interface SubscriptionStatsProps {
  totalSubscriptions: number;
  newSubscriptionsToday: number;
  totalNotifications: number;
  className?: string;
}

export const SubscriptionStats: React.FC<SubscriptionStatsProps> = ({
  totalSubscriptions,
  newSubscriptionsToday,
  totalNotifications,
  className = '',
}) => {
  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">Subscription Stats</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{totalSubscriptions}</div>
          <div className="text-sm text-gray-600">Total Subscriptions</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{newSubscriptionsToday}</div>
          <div className="text-sm text-gray-600">New Today</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">{totalNotifications}</div>
          <div className="text-sm text-gray-600">Notifications</div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionStats;
