/**
 * Centralized Upload Service
 * Handles video uploads to the platform
 */

import { api } from '@/services/api/base';

// Upload data interfaces (stubbed for example)
export interface UploadResponse {
  videoId: string;
  status: string;
}

class UploadService {
  /**
   * Upload a video file
   */
  async uploadVideo(file: File): Promise<UploadResponse> {
    try {
      const response = await api.upload<UploadResponse>('/api/upload/video', file);
      return response.data;
    } catch (error) {
      console.error('Failed to upload video:', error);
      throw error;
    }
  }
}

export const uploadService = new UploadService();
export default uploadService;

