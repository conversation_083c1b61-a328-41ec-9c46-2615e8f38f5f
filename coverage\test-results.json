{"numTotalTestSuites": 10, "numPassedTestSuites": 10, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 12, "numPassedTests": 12, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1751360987782, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["AnalyticsService", "fetchVideoAnalytics"], "fullName": "AnalyticsService fetchVideoAnalytics should fetch analytics data for a video", "status": "passed", "title": "should fetch analytics data for a video", "duration": 7.773400000000038, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["AnalyticsService", "fetchVideoAnalytics"], "fullName": "AnalyticsService fetchVideoAnalytics should throw error when API call fails", "status": "passed", "title": "should throw error when API call fails", "duration": 4.400699999999233, "failureMessages": [], "location": {"line": 39, "column": 5}, "meta": {}}, {"ancestorTitles": ["AnalyticsService", "fetchVideoAnalytics"], "fullName": "AnalyticsService fetchVideoAnalytics should handle different video IDs", "status": "passed", "title": "should handle different video IDs", "duration": 1.2683999999999287, "failureMessages": [], "location": {"line": 48, "column": 5}, "meta": {}}], "startTime": 1751360993467, "endTime": 1751360993480.2683, "status": "passed", "message": "", "name": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/analyticsService.test.ts"}, {"assertionResults": [{"ancestorTitles": ["UploadService", "uploadVideo"], "fullName": "UploadService uploadVideo should upload a video file successfully", "status": "passed", "title": "should upload a video file successfully", "duration": 8.41780000000017, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["UploadService", "uploadVideo"], "fullName": "UploadService uploadVideo should throw error when upload fails", "status": "passed", "title": "should throw error when upload fails", "duration": 4.100999999999658, "failureMessages": [], "location": {"line": 42, "column": 5}, "meta": {}}, {"ancestorTitles": ["UploadService", "uploadVideo"], "fullName": "UploadService uploadVideo should handle different file types", "status": "passed", "title": "should handle different file types", "duration": 1.1941999999999098, "failureMessages": [], "location": {"line": 55, "column": 5}, "meta": {}}, {"ancestorTitles": ["UploadService", "uploadVideo"], "fullName": "UploadService uploadVideo should handle large files", "status": "passed", "title": "should handle large files", "duration": 0.9252000000005864, "failureMessages": [], "location": {"line": 74, "column": 5}, "meta": {}}], "startTime": 1751360993467, "endTime": 1751360993481.9253, "status": "passed", "message": "", "name": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/uploadService.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Video Adapter", "unifiedVideoToVideo"], "fullName": "Video Adapter unifiedVideoToVideo should convert UnifiedVideoMetadata to Video type correctly", "status": "passed", "title": "should convert UnifiedVideoMetadata to Video type correctly", "duration": 9.22190000000046, "failureMessages": [], "location": {"line": 49, "column": 5}, "meta": {}}, {"ancestorTitles": ["Video Adapter", "unifiedVideoToVideo"], "fullName": "Video Adapter unifiedVideoToVideo should handle missing optional fields gracefully", "status": "passed", "title": "should handle missing optional fields gracefully", "duration": 1.7114000000001397, "failureMessages": [], "location": {"line": 93, "column": 5}, "meta": {}}, {"ancestorTitles": ["Video Adapter", "unifiedVideosToVideos"], "fullName": "Video Adapter unifiedVideosToVideos should convert array of UnifiedVideoMetadata to Video array", "status": "passed", "title": "should convert array of UnifiedVideoMetadata to Video array", "duration": 2.8876000000000204, "failureMessages": [], "location": {"line": 140, "column": 5}, "meta": {}}, {"ancestorTitles": ["Video Adapter", "unifiedVideosToVideos"], "fullName": "Video Adapter unifiedVideosToVideos should handle empty array", "status": "passed", "title": "should handle empty array", "duration": 0.4383000000007087, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Video Adapter", "unifiedVideosToVideos"], "fullName": "Video Adapter unifiedVideosToVideos should handle array with one video", "status": "passed", "title": "should handle array with one video", "duration": 0.6008999999994558, "failureMessages": [], "location": {"line": 154, "column": 5}, "meta": {}}], "startTime": 1751360993386, "endTime": 1751360993401.6008, "status": "passed", "message": "", "name": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/adapters/__tests__/videoAdapter.test.ts"}]}