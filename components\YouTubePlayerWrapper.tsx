import React, { useRef, useEffect, forwardRef } from 'react';

export interface YouTubePlayerWrapperProps {
  videoId?: string;
  width?: number | string;
  height?: number | string;
  autoplay?: boolean;
  controls?: boolean;
  mute?: boolean;
  className?: string;
  onReady?: () => void;
  onStateChange?: (state: number) => void;
  onError?: (error: any) => void;
}

const YouTubePlayerWrapper = forwardRef<HTMLDivElement, YouTubePlayerWrapperProps>(
  ({
    videoId,
    width = 560,
    height = 315,
    autoplay = false,
    controls = true,
    mute = false,
    className = '',
    onReady,
    onStateChange,
    onError,
    ...props
  }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const playerRef = useRef<any>(null);

    useEffect(() => {
      if (!videoId) return;

      // Load YouTube API if not already loaded
      if (!(window as any).YT) {
        const tag = document.createElement('script');
        tag.src = 'https://www.youtube.com/iframe_api';
        const firstScriptTag = document.getElementsByTagName('script')[0];
        firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);

        (window as any).onYouTubeIframeAPIReady = initializePlayer;
      } else {
        initializePlayer();
      }

      function initializePlayer() {
        if (!containerRef.current) return;

        playerRef.current = new (window as any).YT.Player(containerRef.current, {
          width,
          height,
          videoId,
          playerVars: {
            autoplay: autoplay ? 1 : 0,
            controls: controls ? 1 : 0,
            mute: mute ? 1 : 0,
            rel: 0,
            modestbranding: 1,
          },
          events: {
            onReady: (event: any) => {
              onReady?.();
            },
            onStateChange: (event: any) => {
              onStateChange?.(event.data);
            },
            onError: (event: any) => {
              onError?.(event.data);
            },
          },
        });
      }

      return () => {
        if (playerRef.current && typeof playerRef.current.destroy === 'function') {
          playerRef.current.destroy();
        }
      };
    }, [videoId, width, height, autoplay, controls, mute, onReady, onStateChange, onError]);

    if (!videoId) {
      return (
        <div 
          ref={ref}
          className={`flex items-center justify-center bg-gray-900 text-white ${className}`}
          style={{ width, height }}
          {...props}
        >
          <div className="text-center">
            <svg className="w-16 h-16 mx-auto mb-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
            </svg>
            <p className="text-gray-400">No video ID provided</p>
          </div>
        </div>
      );
    }

    return (
      <div 
        ref={ref}
        className={`youtube-player-wrapper ${className}`}
        {...props}
      >
        <div ref={containerRef} />
      </div>
    );
  }
);

YouTubePlayerWrapper.displayName = 'YouTubePlayerWrapper';

export default YouTubePlayerWrapper;
