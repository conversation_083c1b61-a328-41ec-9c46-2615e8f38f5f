
> youtubex-studio-clone@2.0.0 lint
> eslint . --ext ts,tsx --report-unused-disable-directives --quiet


C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ChannelTabContent.tsx
  7:27  error  Unable to resolve path to module '../src/components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\Header.tsx
  8:27  error  Unable to resolve path to module '../src/components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\HomeContent.tsx
  3:27  error  Unable to resolve path to module '../src/components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\LiveStreams.tsx
  6:27  error  Unable to resolve path to module '../src/components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\OptimizedVideoCard.tsx
    2:1   error  'react' import is duplicated                                       no-duplicate-imports
   17:30  error  Unable to resolve path to module '../src/lib/youtube-utils'        import/no-unresolved
  611:70  error  Elements with ARIA roles must use a valid, non-abstract ARIA role  jsx-a11y/aria-role
  621:64  error  Elements with ARIA roles must use a valid, non-abstract ARIA role  jsx-a11y/aria-role

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ReusableVideoGrid.tsx
  3:27  error  Unable to resolve path to module '../src/components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\SearchBar.tsx
   6:23  error  Unable to resolve path to module '../../components/icons/ClockIcon'    import/no-unresolved
   7:24  error  Unable to resolve path to module '../../components/icons/SearchIcon'   import/no-unresolved
   8:31  error  Unable to resolve path to module '../../components/SearchSuggestions'  import/no-unresolved
  15:8   error  Unable to resolve path to module '../../services/mockVideoService'     import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\SearchResults.tsx
  5:41  error  Unable to resolve path to module '../../hooks/useIntersectionObserver'  import/no-unresolved
  6:26  error  Unable to resolve path to module '../../utils/componentOptimizations'   import/no-unresolved
  7:36  error  Unable to resolve path to module '../../utils/performance'              import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ShortDisplayCard.tsx
  8:32  error  Unable to resolve path to module '../src/hooks/useVideoPlayer'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\SubscriptionFeed.tsx
  6:27  error  Unable to resolve path to module '../src/components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\SubscriptionsPage.tsx
  2:1  error  'react' import is duplicated  no-duplicate-imports

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\TrendingSection.tsx
  6:27  error  Unable to resolve path to module '../src/components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\UnifiedVideoCard.tsx
  6:31  error  Unable to resolve path to module '../src/components/unified/UnifiedButton'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\VideoMetadata.tsx
   72:46  error  Missing radix parameter  radix
  253:37  error  Missing radix parameter  radix

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\VideoPlaybackDetails.tsx
  3:49  error  Unable to resolve path to module '../src/lib/youtube-utils'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\WatchHistory.tsx
  6:27  error  Unable to resolve path to module '../src/components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\YouTubePlayer.tsx
  3:35  error  Unable to resolve path to module '../src/lib/youtube-utils'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\unified\UnifiedButton.tsx
  9:28  error  Unable to resolve path to module '../../../components/ui/LoadingSpinner'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\config\config\routes.tsx
   4:27  error  Unable to resolve path to module '../components/AccountLayout'        import/no-unresolved
   5:27  error  Unable to resolve path to module '../components/ErrorBoundary'        import/no-unresolved
   6:20  error  Unable to resolve path to module '../components/Layout'               import/no-unresolved
   7:28  error  Unable to resolve path to module '../components/ProtectedRoute'       import/no-unresolved
   8:26  error  Unable to resolve path to module '../components/StudioLayout'         import/no-unresolved
  13:36  error  Unable to resolve path to module '../pages/HomePage'                  import/no-unresolved
  14:37  error  Unable to resolve path to module '../pages/WatchPage'                 import/no-unresolved
  15:45  error  Unable to resolve path to module '../pages/SearchResultsPage'         import/no-unresolved
  16:37  error  Unable to resolve path to module '../pages/LoginPage'                 import/no-unresolved
  17:40  error  Unable to resolve path to module '../pages/RegisterPage'              import/no-unresolved
  18:40  error  Unable to resolve path to module '../pages/TrendingPage'              import/no-unresolved
  19:38  error  Unable to resolve path to module '../pages/ShortsPage'                import/no-unresolved
  20:45  error  Unable to resolve path to module '../pages/SubscriptionsPage'         import/no-unresolved
  21:39  error  Unable to resolve path to module '../pages/HistoryPage'               import/no-unresolved
  22:42  error  Unable to resolve path to module '../pages/WatchLaterPage'            import/no-unresolved
  23:43  error  Unable to resolve path to module '../pages/LikedVideosPage'           import/no-unresolved
  24:39  error  Unable to resolve path to module '../pages/ChannelPage'               import/no-unresolved
  25:36  error  Unable to resolve path to module '../pages/UserPage'                  import/no-unresolved
  26:41  error  Unable to resolve path to module '../pages/PlaylistsPage'             import/no-unresolved
  27:46  error  Unable to resolve path to module '../pages/PlaylistDetailPage'        import/no-unresolved
  28:39  error  Unable to resolve path to module '../pages/LibraryPage'               import/no-unresolved
  29:40  error  Unable to resolve path to module '../pages/YourDataPage'              import/no-unresolved
  30:38  error  Unable to resolve path to module '../pages/GoLivePage'                import/no-unresolved
  31:46  error  Unable to resolve path to module '../pages/AIContentSparkPage'        import/no-unresolved
  32:43  error  Unable to resolve path to module '../pages/VideoUploadPage'           import/no-unresolved
  33:40  error  Unable to resolve path to module '../pages/SettingsPage'              import/no-unresolved
  34:37  error  Unable to resolve path to module '../pages/AdminPage'                 import/no-unresolved
  37:38  error  Unable to resolve path to module '../pages/StudioPage'                import/no-unresolved
  38:38  error  Unable to resolve path to module '../pages/UploadPage'                import/no-unresolved
  39:47  error  Unable to resolve path to module '../pages/StudioDashboardPage'       import/no-unresolved
  40:41  error  Unable to resolve path to module '../pages/AnalyticsPage'             import/no-unresolved
  41:49  error  Unable to resolve path to module '../pages/CommentModerationPage'     import/no-unresolved
  42:44  error  Unable to resolve path to module '../pages/MonetizationPage'          import/no-unresolved
  43:46  error  Unable to resolve path to module '../pages/ContentManagerPage'        import/no-unresolved
  44:45  error  Unable to resolve path to module '../pages/CreatorStudioPage'         import/no-unresolved
  45:41  error  Unable to resolve path to module '../pages/CommunityPage'             import/no-unresolved
  46:47  error  Unable to resolve path to module '../pages/PlaylistManagerPage'       import/no-unresolved
  47:52  error  Unable to resolve path to module '../pages/ChannelCustomizationPage'  import/no-unresolved
  48:43  error  Unable to resolve path to module '../pages/VideoEditorPage'           import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\config\routes.tsx
    1:1   error  There should be at least one empty line between import groups  import/order
    4:1   error  'react-router-dom' import is duplicated                        no-duplicate-imports
   51:19  error  'SearchResultsPage' is not defined                             react/jsx-no-undef
   55:19  error  'TrendingPage' is not defined                                  react/jsx-no-undef
   59:19  error  'ShortsPage' is not defined                                    react/jsx-no-undef
   63:19  error  'SubscriptionsPage' is not defined                             react/jsx-no-undef
   67:19  error  'HistoryPage' is not defined                                   react/jsx-no-undef
   71:19  error  'HistoryPage' is not defined                                   react/jsx-no-undef
   75:19  error  'PlaylistsPage' is not defined                                 react/jsx-no-undef
   79:19  error  'PlaylistDetailPage' is not defined                            react/jsx-no-undef
   83:19  error  'WatchLaterPage' is not defined                                react/jsx-no-undef
   87:19  error  'LikedVideosPage' is not defined                               react/jsx-no-undef
   91:19  error  'ChannelPage' is not defined                                   react/jsx-no-undef
   95:19  error  'UserPage' is not defined                                      react/jsx-no-undef
   99:19  error  'LibraryPage' is not defined                                   react/jsx-no-undef
  103:19  error  'YourDataPage' is not defined                                  react/jsx-no-undef
  107:19  error  'GoLivePage' is not defined                                    react/jsx-no-undef
  111:19  error  'AIContentSparkPage' is not defined                            react/jsx-no-undef
  115:19  error  'VideoUploadPage' is not defined                               react/jsx-no-undef
  119:19  error  'SettingsPage' is not defined                                  react/jsx-no-undef
  123:19  error  'AdminPage' is not defined                                     react/jsx-no-undef
  127:19  error  'StudioPage' is not defined                                    react/jsx-no-undef
  131:19  error  'UploadPage' is not defined                                    react/jsx-no-undef
  136:19  error  'AccountLayout' is not defined                                 react/jsx-no-undef
  140:23  error  'SettingsPage' is not defined                                  react/jsx-no-undef
  144:23  error  'SettingsPage' is not defined                                  react/jsx-no-undef
  148:23  error  'YourDataPage' is not defined                                  react/jsx-no-undef
  154:19  error  'AnalyticsPage' is not defined                                 react/jsx-no-undef
  159:19  error  'ContentManagerPage' is not defined                            react/jsx-no-undef
  169:15  error  'StudioLayout' is not defined                                  react/jsx-no-undef
  174:19  error  'StudioDashboardPage' is not defined                           react/jsx-no-undef
  178:19  error  'AnalyticsPage' is not defined                                 react/jsx-no-undef
  182:19  error  'CommentModerationPage' is not defined                         react/jsx-no-undef
  186:19  error  'MonetizationPage' is not defined                              react/jsx-no-undef
  190:19  error  'ContentManagerPage' is not defined                            react/jsx-no-undef
  194:19  error  'CreatorStudioPage' is not defined                             react/jsx-no-undef
  198:19  error  'CommunityPage' is not defined                                 react/jsx-no-undef
  202:19  error  'PlaylistManagerPage' is not defined                           react/jsx-no-undef
  206:19  error  'ChannelCustomizationPage' is not defined                      react/jsx-no-undef
  210:19  error  'VideoEditorPage' is not defined                               react/jsx-no-undef
  221:8   error  'ProtectedRoute' is not defined                                react/jsx-no-undef
  222:10  error  'LoginPage' is not defined                                     react/jsx-no-undef
  230:8   error  'ProtectedRoute' is not defined                                react/jsx-no-undef
  231:10  error  'RegisterPage' is not defined                                  react/jsx-no-undef

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\AuthContext.tsx
   1:1  error  Imports "React" and "ReactNode" are only used as type  @typescript-eslint/consistent-type-imports
   1:1  error  There should be no empty line within import group      import/order
  13:1  error  Trailing spaces not allowed                            no-trailing-spaces
  17:1  error  Trailing spaces not allowed                            no-trailing-spaces
  38:1  error  Import in body of module; reorder to top               import/first
  38:1  error  'react' import is duplicated                           no-duplicate-imports

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\OptimizedMiniplayerContext.tsx
   1:1  error  Imports "React" and "ReactNode" are only used as type  @typescript-eslint/consistent-type-imports
   1:1  error  There should be no empty line within import group      import/order
  13:1  error  Trailing spaces not allowed                            no-trailing-spaces
  17:1  error  Trailing spaces not allowed                            no-trailing-spaces
  37:1  error  Import in body of module; reorder to top               import/first
  37:1  error  'react' import is duplicated                           no-duplicate-imports
  39:1  error  Import in body of module; reorder to top               import/first

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\ThemeContext.tsx
   1:1  error  Imports "React" and "ReactNode" are only used as type  @typescript-eslint/consistent-type-imports
   1:1  error  There should be no empty line within import group      import/order
  12:1  error  Trailing spaces not allowed                            no-trailing-spaces
  33:1  error  Import in body of module; reorder to top               import/first
  33:1  error  'react' import is duplicated                           no-duplicate-imports

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\UnifiedAppContext.tsx
   1:1  error  Imports "ReactNode" are only used as type          @typescript-eslint/consistent-type-imports
   1:1  error  There should be no empty line within import group  import/order
  27:1  error  Import in body of module; reorder to top           import/first
  27:1  error  'react' import is duplicated                       no-duplicate-imports
  29:1  error  Import in body of module; reorder to top           import/first
  30:1  error  Import in body of module; reorder to top           import/first

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\WatchLaterContext.tsx
   1:1  error  Imports "React" and "ReactNode" are only used as type  @typescript-eslint/consistent-type-imports
   1:1  error  There should be no empty line within import group      import/order
  13:1  error  Trailing spaces not allowed                            no-trailing-spaces
  17:1  error  Trailing spaces not allowed                            no-trailing-spaces
  37:1  error  Import in body of module; reorder to top               import/first
  37:1  error  'react' import is duplicated                           no-duplicate-imports
  39:1  error  Import in body of module; reorder to top               import/first

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\analytics\pages\AnalyticsPage.tsx
  5:27  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved
  6:32  error  Unable to resolve path to module '../utils/numberUtils'          import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\components\ProtectedRoute.tsx
  5:28  error  Unable to resolve path to module '../../../../components/ui/LoadingSpinner'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\pages\LoginPage.tsx
  6:20  error  Unable to resolve path to module '../components/forms/Button'       import/no-unresolved
  7:19  error  Unable to resolve path to module '../components/forms/Input'        import/no-unresolved
  8:25  error  Unable to resolve path to module '../components/icons/YouTubeLogo'  import/no-unresolved
  9:25  error  Unable to resolve path to module '../contexts/AuthContext'          import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\pages\RegisterPage.tsx
  6:20  error  Unable to resolve path to module '../components/forms/Button'       import/no-unresolved
  7:19  error  Unable to resolve path to module '../components/forms/Input'        import/no-unresolved
  8:25  error  Unable to resolve path to module '../components/icons/YouTubeLogo'  import/no-unresolved
  9:25  error  Unable to resolve path to module '../contexts/AuthContext'          import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\channel\pages\ChannelPage.tsx
   8:27   error  Unable to resolve path to module '../components/ChannelHeader'                      import/no-unresolved
   9:31   error  Unable to resolve path to module '../components/ChannelTabContent'                  import/no-unresolved
  10:25   error  Unable to resolve path to module '../components/ChannelTabs'                        import/no-unresolved
  11:33   error  Unable to resolve path to module '../components/LoadingStates/ChannelPageSkeleton'  import/no-unresolved
  12:105  error  Unable to resolve path to module '../services/mockVideoService'                     import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\channel\pages\UserPage.tsx
  8:27  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved
  9:27  error  Unable to resolve path to module '../src/components'             import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\community\pages\CommunityPage.tsx
  2:1  error  'react' import is duplicated  no-duplicate-imports

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\pages\AIContentSparkPage.tsx
  7:36  error  Unable to resolve path to module '../services/geminiService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\pages\CreatorStudioPage.tsx
   2:1   error  'react' import is duplicated                             no-duplicate-imports
  19:37  error  Unable to resolve path to module '../utils/dateUtils'    import/no-unresolved
  20:30  error  Unable to resolve path to module '../utils/numberUtils'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\pages\UploadPage.tsx
   2:1   error  'react' import is duplicated                                       no-duplicate-imports
  15:58  error  Unable to resolve path to module '../components/ui/Tabs'           import/no-unresolved
  16:31  error  Unable to resolve path to module '../components/ui/UnifiedButton'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\pages\VideoUploadPage.tsx
  6:29  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\AIContentSparkPage.tsx
  7:36  error  Unable to resolve path to module '../services/geminiService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\AdminPage.tsx
   2:1  error  'react' import is duplicated                                    no-duplicate-imports
  21:8  error  Unable to resolve path to module '../services/settingsService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\AnalyticsPage.tsx
  5:27  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved
  6:32  error  Unable to resolve path to module '../utils/numberUtils'          import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\ChannelPage.tsx
   8:27   error  Unable to resolve path to module '../components/ChannelHeader'                      import/no-unresolved
   9:31   error  Unable to resolve path to module '../components/ChannelTabContent'                  import/no-unresolved
  10:25   error  Unable to resolve path to module '../components/ChannelTabs'                        import/no-unresolved
  11:33   error  Unable to resolve path to module '../components/LoadingStates/ChannelPageSkeleton'  import/no-unresolved
  12:105  error  Unable to resolve path to module '../services/mockVideoService'                     import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\CommentModerationPage.tsx
  6:49  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved
  7:35  error  Unable to resolve path to module '../utils/dateUtils'            import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\CommunityPage.tsx
  2:1  error  'react' import is duplicated  no-duplicate-imports

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\ContentManagerPage.tsx
  6:27  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved
  7:35  error  Unable to resolve path to module '../utils/dateUtils'            import/no-unresolved
  8:30  error  Unable to resolve path to module '../utils/numberUtils'          import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\CreatorStudioPage.tsx
   2:1   error  'react' import is duplicated                             no-duplicate-imports
  19:37  error  Unable to resolve path to module '../utils/dateUtils'    import/no-unresolved
  20:30  error  Unable to resolve path to module '../utils/numberUtils'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\HistoryPage.tsx
  5:25  error  Unable to resolve path to module '../components/icons/HistoryIcon'                  import/no-unresolved
  6:33  error  Unable to resolve path to module '../components/LoadingStates/HistoryPageSkeleton'  import/no-unresolved
  7:39  error  Unable to resolve path to module '../services/mockVideoService'                     import/no-unresolved
  8:27  error  Unable to resolve path to module '../src/components'                                import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\HomePage.tsx
   7:27  error  Unable to resolve path to module '../components/CategoryChips'  import/no-unresolved
   8:25  error  Unable to resolve path to module '../components/HomeContent'    import/no-unresolved
   9:24  error  Unable to resolve path to module '../components/PageLayout'     import/no-unresolved
  10:27  error  Unable to resolve path to module '../hooks'                     import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\LibraryPage.tsx
   9:30  error  Unable to resolve path to module '../components/icons/HistoryIcon'   import/no-unresolved
  10:32  error  Unable to resolve path to module '../components/icons/PlaylistIcon'  import/no-unresolved
  16:8   error  Unable to resolve path to module '../services/mockVideoService'      import/no-unresolved
  17:27  error  Unable to resolve path to module '../src/components'                 import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\LikedVideosPage.tsx
  7:37  error  Unable to resolve path to module '../components/LoadingStates/LikedVideosPageSkeleton'  import/no-unresolved
  8:32  error  Unable to resolve path to module '../services/mockVideoService'                         import/no-unresolved
  9:27  error  Unable to resolve path to module '../src/components'                                    import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\LoginPage.tsx
  6:20  error  Unable to resolve path to module '../components/forms/Button'       import/no-unresolved
  7:19  error  Unable to resolve path to module '../components/forms/Input'        import/no-unresolved
  8:25  error  Unable to resolve path to module '../components/icons/YouTubeLogo'  import/no-unresolved
  9:25  error  Unable to resolve path to module '../contexts/AuthContext'          import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\OptimizedHomePage.tsx
   6:27  error  Unable to resolve path to module '../components/CategoryChips'         import/no-unresolved
   7:27  error  Unable to resolve path to module '../components/ErrorBoundary'         import/no-unresolved
   8:28  error  Unable to resolve path to module '../components/LoadingSpinner'        import/no-unresolved
   9:34  error  Unable to resolve path to module '../components/VirtualizedVideoGrid'  import/no-unresolved
  10:31  error  Unable to resolve path to module '../hooks/useOptimizedVideoData'      import/no-unresolved
  11:20  error  Unable to resolve path to module '../utils/cn'                         import/no-unresolved
  16:47  error  Unable to resolve path to module '../components/ShortsSection'         import/no-unresolved
  17:49  error  Unable to resolve path to module '../components/TrendingSection'       import/no-unresolved
  18:50  error  Unable to resolve path to module '../components/SubscriptionFeed'      import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\PlaylistDetailPage.tsx
   8:36  error  Unable to resolve path to module '../components/LoadingStates/PlaylistDetailSkeleton'  import/no-unresolved
   9:31  error  Unable to resolve path to module '../components/PlaylistEditModal'                     import/no-unresolved
  10:89  error  Unable to resolve path to module '../services/mockVideoService'                        import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\PlaylistsPage.tsx
  8:54  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\RefactoredContentManagerPage.tsx
  4:22  error  Unable to resolve path to module '../components/BaseForm'            import/no-unresolved
  5:23  error  Unable to resolve path to module '../components/BaseModal'           import/no-unresolved
  6:31  error  Unable to resolve path to module '../components/ReusableVideoGrid'   import/no-unresolved
  7:32  error  Unable to resolve path to module '../components/StandardPageLayout'  import/no-unresolved
  8:24  error  Unable to resolve path to module '../components/ui/Button'           import/no-unresolved
  9:58  error  Unable to resolve path to module '../components/ui/Tabs'             import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\RefactoredTrendingPage.tsx
  3:31  error  Unable to resolve path to module '../components/ReusableVideoGrid'   import/no-unresolved
  4:32  error  Unable to resolve path to module '../components/StandardPageLayout'  import/no-unresolved
  5:31  error  Unable to resolve path to module '../hooks'                          import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\RegisterPage.tsx
  6:20  error  Unable to resolve path to module '../components/forms/Button'       import/no-unresolved
  7:19  error  Unable to resolve path to module '../components/forms/Input'        import/no-unresolved
  8:25  error  Unable to resolve path to module '../components/icons/YouTubeLogo'  import/no-unresolved
  9:25  error  Unable to resolve path to module '../contexts/AuthContext'          import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\SearchResultsPage.tsx
   8:29  error  Unable to resolve path to module '../hooks/useDebounce'             import/no-unresolved
   9:30  error  Unable to resolve path to module '../services/api'                  import/no-unresolved
  10:83  error  Unable to resolve path to module '../services/googleSearchService'  import/no-unresolved
  11:31  error  Unable to resolve path to module '../src/components'                import/no-unresolved
  12:36  error  Unable to resolve path to module '../utils/performance'             import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\SettingsPage.tsx
  8:26  error  Unable to resolve path to module '../contexts/ThemeContext'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\ShortsPage.tsx
  11:26  error  Unable to resolve path to module '../components/CommentModal'                      import/no-unresolved
  12:30  error  Unable to resolve path to module '../components/ErrorStates/EmptyShortsState'      import/no-unresolved
  13:29  error  Unable to resolve path to module '../components/ErrorStates/ShortsPageError'       import/no-unresolved
  14:32  error  Unable to resolve path to module '../components/LoadingStates/ShortsPageSkeleton'  import/no-unresolved
  15:30  error  Unable to resolve path to module '../components/ShortDisplayCard'                  import/no-unresolved
  16:27  error  Unable to resolve path to module '../components/ShortsFilters'                     import/no-unresolved
  17:30  error  Unable to resolve path to module '../components/ShortsNavigation'                  import/no-unresolved
  18:63  error  Unable to resolve path to module '../hooks'                                        import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\StudioDashboardPage.tsx
  18:37  error  Unable to resolve path to module '../utils/dateUtils'    import/no-unresolved
  19:46  error  Unable to resolve path to module '../utils/numberUtils'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\StudioPage.tsx
  21:58  error  Unable to resolve path to module '../components/ui/Tabs'           import/no-unresolved
  22:31  error  Unable to resolve path to module '../components/ui/UnifiedButton'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\SubscriptionsPage.tsx
  14:31  error  Unable to resolve path to module '../components/icons/SubscriptionsIcon'  import/no-unresolved
  15:31  error  Unable to resolve path to module '../components/SubscriptionStats'        import/no-unresolved
  16:35  error  Unable to resolve path to module '../components/SubscriptionVideoCard'    import/no-unresolved
  17:24  error  Unable to resolve path to module '../components/ui/Button'                import/no-unresolved
  18:28  error  Unable to resolve path to module '../components/ui/LoadingSpinner'        import/no-unresolved
  19:58  error  Unable to resolve path to module '../components/ui/Tabs'                  import/no-unresolved
  20:56  error  Unable to resolve path to module '../hooks'                               import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\TrendingPage.tsx
   7:26  error  Unable to resolve path to module '../components/CategoryTabs'  import/no-unresolved
   8:24  error  Unable to resolve path to module '../components/PageLayout'    import/no-unresolved
   9:35  error  Unable to resolve path to module '../hooks'                    import/no-unresolved
  10:27  error  Unable to resolve path to module '../src/components'           import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\UploadPage.tsx
   2:1   error  'react' import is duplicated                                       no-duplicate-imports
  15:58  error  Unable to resolve path to module '../components/ui/Tabs'           import/no-unresolved
  16:31  error  Unable to resolve path to module '../components/ui/UnifiedButton'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\UserPage.tsx
  8:27  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved
  9:27  error  Unable to resolve path to module '../src/components'             import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\VideoUploadPage.tsx
  6:29  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\WatchLaterPage.tsx
  6:31  error  Unable to resolve path to module '../contexts/WatchLaterContext'  import/no-unresolved
  7:27  error  Unable to resolve path to module '../src/components'              import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\WatchPage.tsx
   4:176  error  Unable to resolve path to module '../components'                           import/no-unresolved
   5:27   error  Unable to resolve path to module '../components/VideoMetadata'             import/no-unresolved
   6:38   error  Unable to resolve path to module '../contexts/OptimizedMiniplayerContext'  import/no-unresolved
   7:31   error  Unable to resolve path to module '../contexts/WatchLaterContext'           import/no-unresolved
   8:30   error  Unable to resolve path to module '../hooks/useWatchPage'                   import/no-unresolved
   9:38   error  Unable to resolve path to module '../services/settingsService'             import/no-unresolved
  10:49   error  Unable to resolve path to module '../src/lib/youtube-utils'                import/no-unresolved
  11:37   error  Unable to resolve path to module '../utils/dateUtils'                      import/no-unresolved
  12:29   error  Unable to resolve path to module '../utils/numberUtils'                    import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\pages\YourDataPage.tsx
  7:98  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\moderation\pages\AdminPage.tsx
   2:1  error  'react' import is duplicated                                    no-duplicate-imports
  21:8  error  Unable to resolve path to module '../services/settingsService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\moderation\pages\CommentModerationPage.tsx
  6:49  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved
  7:35  error  Unable to resolve path to module '../utils/dateUtils'            import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\notifications\hooks\useNotifications.ts
  58:7  error  Do not use 'new' for side effects  no-new

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\playlist\pages\PlaylistDetailPage.tsx
   8:36  error  Unable to resolve path to module '../components/LoadingStates/PlaylistDetailSkeleton'  import/no-unresolved
   9:31  error  Unable to resolve path to module '../components/PlaylistEditModal'                     import/no-unresolved
  10:89  error  Unable to resolve path to module '../services/mockVideoService'                        import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\playlist\pages\PlaylistsPage.tsx
  8:54  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\studio\pages\StudioDashboardPage.tsx
  18:37  error  Unable to resolve path to module '../utils/dateUtils'    import/no-unresolved
  19:46  error  Unable to resolve path to module '../utils/numberUtils'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\studio\pages\StudioPage.tsx
  21:58  error  Unable to resolve path to module '../components/ui/Tabs'           import/no-unresolved
  22:31  error  Unable to resolve path to module '../components/ui/UnifiedButton'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\pages\HistoryPage.tsx
  5:25  error  Unable to resolve path to module '../components/icons/HistoryIcon'                  import/no-unresolved
  6:33  error  Unable to resolve path to module '../components/LoadingStates/HistoryPageSkeleton'  import/no-unresolved
  7:39  error  Unable to resolve path to module '../services/mockVideoService'                     import/no-unresolved
  8:27  error  Unable to resolve path to module '../src/components'                                import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\pages\LibraryPage.tsx
   9:30  error  Unable to resolve path to module '../components/icons/HistoryIcon'   import/no-unresolved
  10:32  error  Unable to resolve path to module '../components/icons/PlaylistIcon'  import/no-unresolved
  16:8   error  Unable to resolve path to module '../services/mockVideoService'      import/no-unresolved
  17:27  error  Unable to resolve path to module '../src/components'                 import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\pages\LikedVideosPage.tsx
  7:37  error  Unable to resolve path to module '../components/LoadingStates/LikedVideosPageSkeleton'  import/no-unresolved
  8:32  error  Unable to resolve path to module '../services/mockVideoService'                         import/no-unresolved
  9:27  error  Unable to resolve path to module '../src/components'                                    import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\pages\SettingsPage.tsx
  8:26  error  Unable to resolve path to module '../contexts/ThemeContext'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\pages\SubscriptionsPage.tsx
  14:31  error  Unable to resolve path to module '../components/icons/SubscriptionsIcon'  import/no-unresolved
  15:31  error  Unable to resolve path to module '../components/SubscriptionStats'        import/no-unresolved
  16:35  error  Unable to resolve path to module '../components/SubscriptionVideoCard'    import/no-unresolved
  17:24  error  Unable to resolve path to module '../components/ui/Button'                import/no-unresolved
  18:28  error  Unable to resolve path to module '../components/ui/LoadingSpinner'        import/no-unresolved
  19:58  error  Unable to resolve path to module '../components/ui/Tabs'                  import/no-unresolved
  20:56  error  Unable to resolve path to module '../hooks'                               import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\pages\WatchLaterPage.tsx
  6:31  error  Unable to resolve path to module '../contexts/WatchLaterContext'  import/no-unresolved
  7:27  error  Unable to resolve path to module '../src/components'              import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\pages\YourDataPage.tsx
  7:98  error  Unable to resolve path to module '../services/mockVideoService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\VideoList.tsx
  3:27  error  Unable to resolve path to module '../../components'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\index.ts
  2:38  error  Unable to resolve path to module './VideoCard'  import/no-unresolved
  4:38  error  Unable to resolve path to module './VideoGrid'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\pages\HomePage.tsx
  7:27  error  Unable to resolve path to module '../components/CategoryChips'  import/no-unresolved
  8:25  error  Unable to resolve path to module '../components/HomeContent'    import/no-unresolved
  9:24  error  Unable to resolve path to module '../components/PageLayout'     import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\pages\SearchResultsPage.tsx
   8:29  error  Unable to resolve path to module '../hooks/useDebounce'             import/no-unresolved
   9:30  error  Unable to resolve path to module '../services/api'                  import/no-unresolved
  10:83  error  Unable to resolve path to module '../services/googleSearchService'  import/no-unresolved
  11:31  error  Unable to resolve path to module '../src/components'                import/no-unresolved
  12:36  error  Unable to resolve path to module '../utils/performance'             import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\pages\ShortsPage.tsx
  11:26  error  Unable to resolve path to module '../components/CommentModal'                      import/no-unresolved
  12:30  error  Unable to resolve path to module '../components/ErrorStates/EmptyShortsState'      import/no-unresolved
  13:29  error  Unable to resolve path to module '../components/ErrorStates/ShortsPageError'       import/no-unresolved
  14:32  error  Unable to resolve path to module '../components/LoadingStates/ShortsPageSkeleton'  import/no-unresolved
  15:30  error  Unable to resolve path to module '../components/ShortDisplayCard'                  import/no-unresolved
  16:27  error  Unable to resolve path to module '../components/ShortsFilters'                     import/no-unresolved
  17:30  error  Unable to resolve path to module '../components/ShortsNavigation'                  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\pages\TrendingPage.tsx
   7:26  error  Unable to resolve path to module '../components/CategoryTabs'  import/no-unresolved
   8:24  error  Unable to resolve path to module '../components/PageLayout'    import/no-unresolved
  10:27  error  Unable to resolve path to module '../src/components'           import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\pages\WatchPage.tsx
   5:27  error  Unable to resolve path to module '../components/VideoMetadata'             import/no-unresolved
   6:38  error  Unable to resolve path to module '../contexts/OptimizedMiniplayerContext'  import/no-unresolved
   7:31  error  Unable to resolve path to module '../contexts/WatchLaterContext'           import/no-unresolved
   8:30  error  Unable to resolve path to module '../hooks/useWatchPage'                   import/no-unresolved
   9:38  error  Unable to resolve path to module '../services/settingsService'             import/no-unresolved
  10:49  error  Unable to resolve path to module '../src/lib/youtube-utils'                import/no-unresolved
  11:37  error  Unable to resolve path to module '../utils/dateUtils'                      import/no-unresolved
  12:29  error  Unable to resolve path to module '../utils/numberUtils'                    import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\unified\__tests__\useVideos.test.tsx
   77:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  122:16  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  148:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  167:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  179:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  228:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  284:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  316:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  331:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  341:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  491:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\useVideosData.ts
  4:36  error  Unable to resolve path to module '../src/services/unifiedDataService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\useWatchPage.ts
  6:36  error  Unable to resolve path to module '../src/services/unifiedDataService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\pages\HomePage.tsx
   1:1   error  All imports in the declaration are only used as types. Use `import type`  @typescript-eslint/consistent-type-imports
  13:1   error  Trailing spaces not allowed                                               no-trailing-spaces
  25:17  error  Trailing spaces not allowed                                               no-trailing-spaces
  26:42  error  Trailing spaces not allowed                                               no-trailing-spaces
  32:1   error  Trailing spaces not allowed                                               no-trailing-spaces
  38:17  error  Trailing spaces not allowed                                               no-trailing-spaces
  39:56  error  Trailing spaces not allowed                                               no-trailing-spaces
  45:1   error  Trailing spaces not allowed                                               no-trailing-spaces
  51:17  error  Trailing spaces not allowed                                               no-trailing-spaces
  52:46  error  Trailing spaces not allowed                                               no-trailing-spaces

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\pages\WatchPage.tsx
   1:1  error  All imports in the declaration are only used as types. Use `import type`  @typescript-eslint/consistent-type-imports
  13:1  error  Trailing spaces not allowed                                               no-trailing-spaces

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\__tests__\analyticsService.test.ts
  10:1  error  '../analyticsService' import is duplicated  no-duplicate-imports

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\__tests__\searchService.test.ts
   78:17  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
   90:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  106:17  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  116:17  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  125:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  186:17  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  209:17  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\__tests__\unifiedDataService.test.ts
    3:35  error  Unable to resolve path to module '../../../services/mockVideoService'                                                                                                                                           import/no-unresolved
  244:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method
  301:14  error  Avoid referencing unbound methods which may cause unintentional scoping of `this`.
If your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  @typescript-eslint/unbound-method

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\__tests__\uploadService.test.ts
  10:1  error  '../uploadService' import is duplicated  no-duplicate-imports

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\unifiedDataService.ts
  1:35  error  Unable to resolve path to module '../../services/mockVideoService'  import/no-unresolved

C:\Users\<USER>\Documents\GitHub\yt\ytmain5\test\test-utils.tsx
  2:1  error  'react' import is duplicated  no-duplicate-imports

Γ£û 382 problems (382 errors, 0 warnings)
  30 errors and 0 warnings potentially fixable with the `--fix` option.

