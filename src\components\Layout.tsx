import * as React from 'react';
import { Outlet } from 'react-router-dom';

import Header from './Header';
import { Sidebar } from './ui';

interface LayoutProps {
  children?: React.ReactNode;
}

/**
 * Main application layout component that provides the overall structure
 * with header, sidebar, and main content area.
 */
const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-4">
          {children || <Outlet />}
        </main>
      </div>
    </div>
  );
};

export default Layout;