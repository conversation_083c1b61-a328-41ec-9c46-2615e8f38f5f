import React from 'react';

interface PlaylistDetailSkeletonProps {
  className?: string;
}

export const PlaylistDetailSkeleton: React.FC<PlaylistDetailSkeletonProps> = ({ 
  className = '' 
}) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {/* Header skeleton */}
      <div className="mb-6">
        <div className="h-8 bg-gray-300 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-300 rounded w-1/3"></div>
      </div>

      {/* Video list skeleton */}
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex space-x-4">
            <div className="w-32 h-20 bg-gray-300 rounded"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-1/2 mb-1"></div>
              <div className="h-3 bg-gray-300 rounded w-1/4"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PlaylistDetailSkeleton;
