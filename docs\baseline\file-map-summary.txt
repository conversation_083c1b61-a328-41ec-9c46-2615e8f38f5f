
File Map Analysis Summary
========================
Generated: 2025-07-01T11:26:29.465Z

📊 STATISTICS
- Total TypeScript files: 373
- Files with imports: 279
- Files that are imported: 0
- Potential dead files: 373
- External dependencies: 296

🔥 HEAVILY IMPORTED FILES (Top 10)


💀 POTENTIAL DEAD FILES (Not imported by any other file)
- src\vite-env.d.ts
- src\main.tsx
- src\App.tsx
- src\utils\videoUtils.ts
- src\utils\uploadUtils.ts
- src\utils\unifiedUtils.ts
- src\utils\testing.tsx
- src\utils\test-setup.ts
- src\utils\playerUtils.ts
- src\utils\performance.ts
- src\utils\numberUtils.ts
- src\utils\normalizeVideo.ts
- src\utils\imageUtils.ts
- src\utils\formatters.ts
- src\utils\dateUtils.ts
- src\utils\componentUtils.tsx
- src\utils\componentOptimizations.ts
- src\utils\cn.ts
- src\utils\analyticsUtils.ts
- src\types\video.ts
- src\types\unifiedTypes.ts
- src\types\strictTypes.ts
- src\types\legacy.ts
- src\types\index.ts
- src\types\core.ts
- src\store\index.ts
- src\services\uploadService.ts
- src\services\unifiedDataService.ts
- src\services\unifiedApiService.ts
- src\services\settingsService.ts
- src\services\searchService.ts
- src\services\mockVideoService.ts
- src\services\mockApiService.ts
- src\services\metadataNormalizationService.ts
- src\services\index.ts
- src\services\googleSearchService.ts
- src\services\geminiService.ts
- src\services\errorService.ts
- src\services\api.ts
- src\services\analyticsService.ts
- src\pages\YouTubeDemo.tsx
- src\pages\WatchPage.tsx
- src\pages\HomePage.tsx
- src\providers\UnifiedProviders.tsx
- src\providers\RefactoredAppProviders.tsx
- src\providers\AppProviders.tsx
- src\lib\youtube-utils.ts
- src\lib\utils.ts
- src\lib\constants.ts
- src\hooks\useWatchPage.ts
- src\hooks\useVideosData.ts
- src\hooks\useVideoPlayer.ts
- src\hooks\useVideoData.ts
- src\hooks\useVideoCache.ts
- src\hooks\useVideoAutoplay.ts
- src\hooks\useSubscriptions.ts
- src\hooks\useShortsData.ts
- src\hooks\useRefactoredHooks.ts
- src\hooks\usePerformanceMonitor.ts
- src\hooks\usePagination.ts
- src\hooks\useOptimizedVideoData.ts
- src\hooks\useModal.ts
- src\hooks\useLocalStorageSet.ts
- src\hooks\useLocalStorage.ts
- src\hooks\useIntersectionObserver.ts
- src\hooks\useFormState.ts
- src\hooks\useDropdownMenu.ts
- src\hooks\useDebounce.ts
- src\hooks\useCommon.ts
- src\hooks\useAsyncState.ts
- src\hooks\useAsyncData.ts
- src\hooks\useAnalytics.ts
- src\hooks\unifiedHooks.ts
- src\hooks\index.ts
- src\features\index.ts
- src\contexts\WatchLaterContext.tsx
- src\contexts\UnifiedAppContext.tsx
- src\contexts\ThemeContext.tsx
- src\contexts\OptimizedMiniplayerContext.tsx
- src\contexts\MiniplayerContext.tsx
- src\contexts\AuthContext.tsx
- src\config\routes.tsx
- src\components\YouTubeVideoGrid.tsx
- src\components\YouTubeVideoCard.tsx
- src\components\YouTubePlayerWrapper.tsx
- src\components\YouTubePlayer.tsx
- src\components\WatchHistory.tsx
- src\components\VirtualizedVideoGrid.tsx
- src\components\VideoQualitySelector.tsx
- src\components\VideoPlaybackDetails.tsx
- src\components\VideoMetadata.tsx
- src\components\VideoGrid.tsx
- src\components\VideoEditor.tsx
- src\components\VideoDescription.tsx
- src\components\VideoCard.tsx
- src\components\VideoAnalyticsDashboard.tsx
- src\components\VideoAnalytics.tsx
- src\components\VideoActions.tsx
- src\components\UserMenu.tsx
- src\components\UnifiedVideoCard.tsx
- src\components\TrendingSection.tsx
- src\components\SuspenseWrapper.tsx
- src\components\SubscriptionVideoCard.tsx
- src\components\SubscriptionStats.tsx
- src\components\SubscriptionsPage.tsx
- src\components\SubscriptionManager.tsx
- src\components\SubscriptionFeed.tsx
- src\components\StudioLayout.tsx
- src\components\StandardPageLayout.tsx
- src\components\SortFilterPanel.tsx
- src\components\Sidebar.tsx
- src\components\ShortsSection.tsx
- src\components\ShortsProgressIndicator.tsx
- src\components\ShortsPlayer.tsx
- src\components\ShortsNavigation.tsx
- src\components\ShortsFilters.tsx
- src\components\ShortDisplayCard.tsx
- src\components\SearchSuggestions.tsx
- src\components\SearchResults.tsx
- src\components\SearchBar.tsx
- src\components\ReusableVideoGrid.tsx
- src\components\RefactoredVideoDescription.tsx
- src\components\RefactoredSaveToPlaylistModal.tsx
- src\components\RecommendationEngine.tsx
- src\components\ProtectedRoute.tsx
- src\components\PlaylistEditModal.tsx
- src\components\PictureInPicture.tsx
- src\components\PageLayout.tsx
- src\components\OptimizedVideoCard.tsx
- src\components\NotificationSystem.tsx
- src\components\NotificationsPanel.tsx
- src\components\NotificationCenter.tsx
- src\components\ModerationDashboard.tsx
- src\components\Modal.tsx
- src\components\Miniplayer.tsx
- src\components\LoadingStates.tsx
- src\components\LoadingSpinner.tsx
- src\components\LiveStreams.tsx
- src\components\LiveStreamManager.tsx
- src\components\Layout.tsx
- src\components\index.ts
- src\components\ImageWithFallback.tsx
- src\components\HomeContent.tsx
- src\components\Header.tsx
- src\components\ErrorBoundary.tsx
- src\components\EnhancedVideoUpload.tsx
- src\components\EnhancedCommentSystem.tsx
- src\components\DataWrapper.tsx
- src\components\ConsolidatedVideoCard.tsx
- src\components\CommunityPosts.tsx
- src\components\CommentsSection.tsx
- src\components\CommentModal.tsx
- src\components\ChannelTabs.tsx
- src\components\ChannelTabContent.tsx
- src\components\ChannelHeader.tsx
- src\components\CategoryTabs.tsx
- src\components\CategoryChips.tsx
- src\components\BaseModal.tsx
- src\components\BaseForm.tsx
- src\components\AdvancedVideoPlayer.tsx
- src\components\AdvancedSearch.tsx
- src\components\AddCommentForm.tsx
- src\components\AccountLayout.tsx
- src\utils\mocks\youtubeApiTestHandlers.ts
- src\utils\mocks\server.ts
- src\utils\mocks\handlers.ts
- src\utils\mocks\browser.ts
- src\services\__tests__\uploadService.test.ts
- src\services\__tests__\unifiedDataService.test.ts
- src\services\__tests__\unifiedDataService.simple.test.ts
- src\services\__tests__\searchService.test.ts
- src\services\__tests__\extractYouTubeId.spec.ts
- src\services\__tests__\analyticsService.test.ts
- src\services\api\youtubeService.ts
- src\services\api\videos.ts
- src\services\api\base.ts
- src\services\adapters\videoAdapter.ts
- src\hooks\unified\useVideos.ts
- src\hooks\unified\useApi.ts
- src\hooks\unified\index.ts
- src\features\video\index.ts
- src\features\common\index.ts
- src\config\config\routes.tsx
- src\config\config\index.ts
- src\features\auth\index.ts
- src\components\__tests__\ErrorBoundary.test.tsx
- src\components\unified\UnifiedVideoCard.tsx
- src\components\unified\UnifiedButton.tsx
- src\components\unified\index.ts
- src\components\video\VideoSettings.tsx
- src\components\video\VideoControls.tsx
- src\components\video\index.ts
- src\components\ui\UnifiedComponents.tsx
- src\components\ui\UnifiedButton.tsx
- src\components\ui\Tabs.tsx
- src\components\ui\ProgressBar.tsx
- src\components\ui\LoadingStates.tsx
- src\components\ui\LoadingSpinner.tsx
- src\components\ui\index.ts
- src\components\ui\FormComponents.tsx
- src\components\ui\FileUpload.tsx
- src\components\ui\ErrorMessage.tsx
- src\components\ui\DropdownMenu.tsx
- src\components\ui\Button.tsx
- src\components\ui\Badge.tsx
- src\components\ui\ActionButton.tsx
- src\components\molecules\index.ts
- src\components\organisms\index.ts
- src\components\LoadingStates\WatchPageSkeleton.tsx
- src\components\LoadingStates\ShortsPageSkeleton.tsx
- src\components\LoadingStates\PlaylistDetailSkeleton.tsx
- src\components\LoadingStates\LikedVideosPageSkeleton.tsx
- src\components\LoadingStates\HistoryPageSkeleton.tsx
- src\components\LoadingStates\ChannelPageSkeleton.tsx
- src\components\icons\YouTubeLogo.tsx
- src\components\icons\VideoPlusIcon.tsx
- src\components\icons\UserIcon.tsx
- src\components\icons\UnifiedIcon.tsx
- src\components\icons\ThumbsUpIcon.tsx
- src\components\icons\ThumbsDownIcon.tsx
- src\components\icons\SummarizeIcon.tsx
- src\components\icons\SubscriptionsIcon.tsx
- src\components\icons\ShortsIcon.tsx
- src\components\icons\ShareIcon.tsx
- src\components\icons\SearchIcon.tsx
- src\components\icons\SaveIconFilled.tsx
- src\components\icons\SaveIcon.tsx
- src\components\icons\PlaylistPlayIcon.tsx
- src\components\icons\PlaylistIcon.tsx
- src\components\icons\MenuIcon.tsx
- src\components\icons\HomeIcon.tsx
- src\components\icons\HistoryIcon.tsx
- src\components\icons\HeartIcon.tsx
- src\components\icons\FireIcon.tsx
- src\components\icons\ClockIcon.tsx
- src\components\icons\ChatBubbleOvalLeftEllipsisIcon.tsx
- src\components\icons\BellIcon.tsx
- src\components\icons\ArrowUturnRightIcon.tsx
- src\components\icons\AdjustmentsHorizontalIcon.tsx
- src\components\examples\YouTubePlayerExample.tsx
- src\components\forms\UnifiedFormSystem.tsx
- src\components\forms\Textarea.tsx
- src\components\forms\Input.tsx
- src\components\forms\Button.tsx
- src\components\ErrorStates\VideoNotFound.tsx
- src\components\ErrorStates\ShortsPageError.tsx
- src\components\ErrorStates\EmptyShortsState.tsx
- src\components\atoms\index.ts
- src\services\api\__tests__\youtubeService.test.ts
- src\services\api\__tests__\buildUrl.test.ts
- src\services\adapters\__tests__\videoAdapter.test.ts
- src\hooks\unified\__tests__\useVideos.test.tsx
- src\features\video\types\index.ts
- src\features\video\services\videoService.ts
- src\features\video\services\index.ts
- src\features\video\mocks\videoMocks.ts
- src\features\video\pages\WatchPage.tsx
- src\features\video\pages\VideoDemo.tsx
- src\features\video\pages\TrendingPage.tsx
- src\features\video\pages\ShortsPage.tsx
- src\features\video\pages\SearchResultsPage.tsx
- src\features\video\pages\index.ts
- src\features\video\pages\HomePage.tsx
- src\features\video\hooks\useVideoInteractions.ts
- src\features\video\hooks\useVideo.ts
- src\features\video\hooks\index.ts
- src\features\video\components\VideoUpload.tsx
- src\features\video\components\VideoPlayer.tsx
- src\features\video\components\VideoList.tsx
- src\features\video\components\VideoInteractions.tsx
- src\features\video\components\VideoEditor.tsx
- src\features\video\components\StudioVideoGrid.tsx
- src\features\video\components\index.ts
- src\features\user\pages\YourDataPage.tsx
- src\features\user\pages\WatchLaterPage.tsx
- src\features\user\pages\SubscriptionsPage.tsx
- src\features\user\pages\SettingsPage.tsx
- src\features\user\pages\LikedVideosPage.tsx
- src\features\user\pages\LibraryPage.tsx
- src\features\user\pages\HistoryPage.tsx
- src\features\subscription\services\subscriptionService.ts
- src\features\subscription\hooks\useSubscription.ts
- src\features\subscription\components\SubscriptionButton.tsx
- src\features\studio\pages\StudioPage.tsx
- src\features\studio\pages\StudioDashboardPage.tsx
- src\features\playlist\services\playlistService.ts
- src\features\playlist\pages\PlaylistsPage.tsx
- src\features\playlist\pages\PlaylistDetailPage.tsx
- src\features\search\components\AdvancedSearchFilters.tsx
- src\features\search\services\searchService.ts
- src\features\playlist\hooks\usePlaylists.ts
- src\features\playlist\components\PlaylistManager.tsx
- src\features\playlist\components\PlaylistCard.tsx
- src\features\notifications\hooks\useNotifications.ts
- src\features\notifications\services\notificationService.ts
- src\features\notifications\components\NotificationCenter.tsx
- src\features\moderation\pages\CommentModerationPage.tsx
- src\features\moderation\pages\AdminPage.tsx
- src\features\moderation\components\ModerationDashboard.tsx
- src\features\creator\pages\VideoUploadPage.tsx
- src\features\creator\pages\VideoEditorPage.tsx
- src\features\creator\pages\UploadPage.tsx
- src\features\creator\pages\GoLivePage.tsx
- src\features\creator\pages\DashboardPage.tsx
- src\features\creator\pages\CreatorStudioPage.tsx
- src\features\creator\pages\AIContentSparkPage.tsx
- src\features\creator\components\CreatorStudioDashboard.tsx
- src\features\community\pages\CommunityPage.tsx
- src\features\common\components\StudioSidebar.tsx
- src\features\common\components\StudioLayout.tsx
- src\features\common\components\StudioHeader.tsx
- src\features\common\components\Sidebar.tsx
- src\features\common\components\Layout.tsx
- src\features\common\components\Header.tsx
- src\features\common\components\Footer.tsx
- src\features\common\components\ErrorBoundary.tsx
- src\features\livestream\components\LiveStreamStudio.tsx
- src\features\community\components\CommunityPost.tsx
- src\features\misc\pages\YourDataPage.tsx
- src\features\misc\pages\WatchPage.tsx
- src\features\misc\pages\WatchLaterPage.tsx
- src\features\misc\pages\VideoUploadPage.tsx
- src\features\misc\pages\VideoEditorPage.tsx
- src\features\misc\pages\UserPage.tsx
- src\features\misc\pages\UploadPage.tsx
- src\features\misc\pages\TrendingPage.tsx
- src\features\misc\pages\SubscriptionsPage.tsx
- src\features\misc\pages\StudioPage.tsx
- src\features\misc\pages\StudioDashboardPage.tsx
- src\features\misc\pages\ShortsPage.tsx
- src\features\misc\pages\SettingsPage.tsx
- src\features\misc\pages\SearchResultsPage.tsx
- src\features\misc\pages\RegisterPage.tsx
- src\features\misc\pages\RefactoredTrendingPage.tsx
- src\features\misc\pages\RefactoredContentManagerPage.tsx
- src\features\misc\pages\PlaylistsPage.tsx
- src\features\misc\pages\PlaylistManagerPage.tsx
- src\features\misc\pages\PlaylistDetailPage.tsx
- src\features\misc\pages\OptimizedHomePage.tsx
- src\features\misc\pages\MonetizationPage.tsx
- src\features\misc\pages\LoginPage.tsx
- src\features\misc\pages\LikedVideosPage.tsx
- src\features\misc\pages\LibraryPage.tsx
- src\features\misc\pages\HomePage.tsx
- src\features\misc\pages\HistoryPage.tsx
- src\features\misc\pages\GoLivePage.tsx
- src\features\misc\pages\CreatorStudioPage.tsx
- src\features\misc\pages\ContentManagerPage.tsx
- src\features\misc\pages\CommunityPage.tsx
- src\features\misc\pages\CommentModerationPage.tsx
- src\features\misc\pages\ChannelPage.tsx
- src\features\misc\pages\ChannelCustomizationPage.tsx
- src\features\misc\pages\AnalyticsPage.tsx
- src\features\misc\pages\AIContentSparkPage.tsx
- src\features\misc\pages\AdminPage.tsx
- src\features\comments\services\commentService.ts
- src\features\comments\components\CommentSection.tsx
- src\features\comments\hooks\useComments.ts
- src\features\auth\types\index.ts
- src\features\auth\store\authStore.ts
- src\features\auth\services\authService.ts
- src\features\auth\pages\RegisterPage.tsx
- src\features\auth\pages\LoginPage.tsx
- src\features\auth\components\RegisterForm.tsx
- src\features\auth\components\ProtectedRoute.tsx
- src\features\auth\components\LoginForm.tsx
- src\features\analytics\components\AdvancedAnalyticsDashboard.tsx
- src\features\analytics\pages\AnalyticsPage.tsx
- src\features\channel\pages\UserPage.tsx
- src\features\channel\pages\ChannelPage.tsx
- src\components\atoms\Button\index.ts
- src\components\atoms\Button\Button.tsx
- src\features\video\services\__tests__\videoService.test.ts

📦 EXTERNAL DEPENDENCIES
- @/App
- @/config/routes
- @/providers/RefactoredAppProviders
- @/styles.css
- @google/genai
- @heroicons/react/24/outline
- @heroicons/react/24/solid
- @tanstack/react-query
- @testing-library/jest-dom
- @testing-library/react
- @testing-library/user-event
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\components\SearchSuggestions
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\components\icons\ClockIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\components\icons\SearchIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\components\ui\LoadingSpinner
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\hooks\useIntersectionObserver
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\AddCommentForm
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\AdvancedVideoPlayer
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\BaseForm
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\BaseModal
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\DataWrapper
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ErrorBoundary
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\Header
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\LiveStreams
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\LoadingSpinner
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\LoadingStates
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\Miniplayer
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\NotificationSystem
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\OptimizedVideoCard
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ShortDisplayCard
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ShortsSection
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\Sidebar
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\StudioHeader
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\StudioSidebar
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\SubscriptionFeed
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\SuspenseWrapper
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\TrendingSection
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\UserMenu
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\VideoActions
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\VideoCard
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\VideoDescription
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\WatchHistory
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\YouTubePlayer
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\YouTubePlayerWrapper
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\YouTubeVideoCard
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\atoms\Button
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\examples\YouTubePlayerExample
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\forms\Button
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\AdjustmentsHorizontalIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\BellIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\ClockIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\FireIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\HistoryIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\HomeIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\MenuIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\PlaylistIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\PlaylistPlayIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\SaveIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\SaveIconFilled
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\SearchIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\ShortsIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\SubscriptionsIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\SummarizeIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\ThumbsDownIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\ThumbsUpIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\UnifiedIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\VideoPlusIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\icons\YouTubeLogo
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ui
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ui\Button
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ui\DropdownMenu
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ui\LoadingStates
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\ui\UnifiedButton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\unified
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\components\unified\UnifiedButton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\config\components\AccountLayout
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\config\components\ErrorBoundary
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\config\components\Layout
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\config\components\ProtectedRoute
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\config\components\StudioLayout
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\AuthContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\MiniplayerContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\OptimizedMiniplayerContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\ThemeContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\UnifiedAppContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\contexts\WatchLaterContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\analytics\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\analytics\utils\numberUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\components\forms\Button
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\components\forms\Input
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\components\icons\YouTubeLogo
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\contexts\AuthContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\services\authService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\auth\store\authStore
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\channel\components\ChannelHeader
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\channel\components\ChannelTabContent
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\channel\components\ChannelTabs
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\channel\components\LoadingStates\ChannelPageSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\channel\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\channel\src\components
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\comments\hooks\useComments
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\comments\services\commentService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\common\components\Footer
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\common\components\Header
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\common\components\Sidebar
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\common\components\StudioHeader
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\common\components\StudioSidebar
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\components
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\components\ui\Tabs
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\components\ui\UnifiedButton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\services\geminiService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\utils\dateUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\creator\utils\numberUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\BaseForm
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\BaseModal
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\CategoryChips
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\CategoryTabs
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ChannelHeader
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ChannelTabContent
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ChannelTabs
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\CommentModal
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ErrorBoundary
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ErrorStates\EmptyShortsState
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ErrorStates\ShortsPageError
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\HomeContent
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\LoadingSpinner
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\LoadingStates\ChannelPageSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\LoadingStates\HistoryPageSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\LoadingStates\LikedVideosPageSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\LoadingStates\PlaylistDetailSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\LoadingStates\ShortsPageSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\PageLayout
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\PlaylistEditModal
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ReusableVideoGrid
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ShortDisplayCard
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ShortsFilters
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ShortsNavigation
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\StandardPageLayout
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\SubscriptionStats
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\SubscriptionVideoCard
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\VideoMetadata
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\VirtualizedVideoGrid
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\forms\Button
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\forms\Input
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\icons\HistoryIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\icons\PlaylistIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\icons\SubscriptionsIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\icons\YouTubeLogo
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ui\Button
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ui\LoadingSpinner
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ui\Tabs
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\components\ui\UnifiedButton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\contexts\AuthContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\contexts\OptimizedMiniplayerContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\contexts\ThemeContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\contexts\WatchLaterContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\hooks
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\hooks\useDebounce
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\hooks\useOptimizedVideoData
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\hooks\useWatchPage
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\services\api
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\services\geminiService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\services\googleSearchService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\services\settingsService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\src\components
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\src\lib\youtube-utils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\utils\cn
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\utils\dateUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\utils\numberUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\misc\utils\performance
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\moderation\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\moderation\services\settingsService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\moderation\utils\dateUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\notifications\hooks\useNotifications
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\notifications\services\notificationService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\playlist\components\LoadingStates\PlaylistDetailSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\playlist\components\PlaylistEditModal
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\playlist\hooks\usePlaylists
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\playlist\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\playlist\services\playlistService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\studio\components\ui\Tabs
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\studio\components\ui\UnifiedButton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\studio\utils\dateUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\studio\utils\numberUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\subscription\hooks\useSubscription
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\subscription\services\subscriptionService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\LoadingStates\HistoryPageSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\LoadingStates\LikedVideosPageSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\SubscriptionStats
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\SubscriptionVideoCard
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\icons\HistoryIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\icons\PlaylistIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\icons\SubscriptionsIcon
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\ui\Button
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\ui\LoadingSpinner
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\components\ui\Tabs
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\contexts\ThemeContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\contexts\WatchLaterContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\hooks
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\user\src\components
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\CategoryChips
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\CategoryTabs
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\CommentModal
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\ErrorStates\EmptyShortsState
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\ErrorStates\ShortsPageError
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\HomeContent
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\LoadingStates\ShortsPageSkeleton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\PageLayout
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\ShortDisplayCard
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\ShortsFilters
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\ShortsNavigation
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\components\VideoMetadata
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\contexts\OptimizedMiniplayerContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\contexts\WatchLaterContext
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\hooks
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\hooks\useDebounce
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\hooks\useVideoInteractions
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\hooks\useWatchPage
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\mocks\videoMocks
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\services\api
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\services\googleSearchService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\services\settingsService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\services\videoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\src\components
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\src\lib\youtube-utils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\utils\dateUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\utils\numberUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\features\video\utils\performance
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\unifiedHooks
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\unified\useApi
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\unified\useVideos
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\useAsyncData
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\useDropdownMenu
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\useIntersectionObserver
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\usePerformanceMonitor
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\hooks\useRefactoredHooks
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\lib\constants
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\lib\utils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\lib\youtube-utils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\pages\components\examples\YouTubePlayerExample
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\pages\lib\youtube-utils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\providers\UnifiedProviders
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\adapters\videoAdapter
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\analyticsService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\api\base
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\api\videos
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\api\youtubeService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\metadataNormalizationService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\mockVideoService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\searchService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\settingsService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\unifiedDataService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\services\uploadService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\src\components
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\src\components\unified\UnifiedButton
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\src\hooks\useVideoPlayer
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\src\lib\youtube-utils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\src\services\unifiedDataService
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\cn
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\componentOptimizations
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\componentUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\dateUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\formatters
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\mocks\handlers
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\mocks\server
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\numberUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\performance
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\src\utils\unifiedUtils
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\utils\componentOptimizations
- C:\Users\<USER>\Documents\GitHub\yt\ytmain5\utils\performance
- chart.js
- class-variance-authority
- clsx
- date-fns
- lucide-react
- msw
- msw/browser
- msw/node
- react
- react-beautiful-dnd
- react-chartjs-2
- react-dom/client
- react-router-dom
- react-window
- tailwind-merge
- vitest
- zustand
- zustand/middleware
- zustand/middleware/immer
