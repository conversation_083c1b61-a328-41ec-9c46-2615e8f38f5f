import { lazy } from 'react';

import { Outlet } from 'react-router-dom';

import type { RouteObject } from 'react-router-dom';

// Basic placeholder components for manual verification
const BasicLayout = () => (
  <div className="min-h-screen bg-white dark:bg-gray-900">
    <div className="max-w-7xl mx-auto px-4 py-6">
      <main>
        <Outlet />
      </main>
    </div>
  </div>
);

const ErrorBoundary = () => (
  <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
    <div className="text-center">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Error</h1>
      <p className="text-gray-600 dark:text-gray-400">Something went wrong.</p>
    </div>
  </div>
);

// Lazy load all the pages (using correct paths based on actual file locations)
const HomePage = lazy(() => import('@/features/misc/pages/HomePage'));
const WatchPage = lazy(() => import('@/features/video/pages/WatchPage'));
const SearchResultsPage = lazy(() => import('@/features/video/pages/SearchResultsPage'));
const TrendingPage = lazy(() => import('@/features/video/pages/TrendingPage'));
const ShortsPage = lazy(() => import('@/features/video/pages/ShortsPage'));
const SubscriptionsPage = lazy(() => import('@/features/user/pages/SubscriptionsPage'));
const HistoryPage = lazy(() => import('@/features/user/pages/HistoryPage'));
const PlaylistsPage = lazy(() => import('@/features/playlist/pages/PlaylistsPage'));
const PlaylistDetailPage = lazy(() => import('@/features/playlist/pages/PlaylistDetailPage'));
const WatchLaterPage = lazy(() => import('@/features/user/pages/WatchLaterPage'));
const LikedVideosPage = lazy(() => import('@/features/user/pages/LikedVideosPage'));
const ChannelPage = lazy(() => import('@/features/channel/pages/ChannelPage'));
const UserPage = lazy(() => import('@/features/channel/pages/UserPage'));
const LibraryPage = lazy(() => import('@/features/user/pages/LibraryPage'));
const YourDataPage = lazy(() => import('@/features/user/pages/YourDataPage'));
const GoLivePage = lazy(() => import('@/features/creator/pages/GoLivePage'));
const AIContentSparkPage = lazy(() => import('@/features/creator/pages/AIContentSparkPage'));
const VideoUploadPage = lazy(() => import('@/features/creator/pages/VideoUploadPage'));
const SettingsPage = lazy(() => import('@/features/user/pages/SettingsPage'));
const AdminPage = lazy(() => import('@/features/moderation/pages/AdminPage'));
const StudioPage = lazy(() => import('@/features/studio/pages/StudioPage'));
const UploadPage = lazy(() => import('@/features/creator/pages/UploadPage'));
const AnalyticsPage = lazy(() => import('@/features/analytics/pages/AnalyticsPage'));
const ContentManagerPage = lazy(() => import('@/features/misc/pages/ContentManagerPage'));
const StudioDashboardPage = lazy(() => import('@/features/studio/pages/StudioDashboardPage'));
const CommentModerationPage = lazy(() => import('@/features/moderation/pages/CommentModerationPage'));
const MonetizationPage = lazy(() => import('@/features/misc/pages/MonetizationPage'));
const CreatorStudioPage = lazy(() => import('@/features/creator/pages/CreatorStudioPage'));
const CommunityPage = lazy(() => import('@/features/community/pages/CommunityPage'));
const PlaylistManagerPage = lazy(() => import('@/features/misc/pages/PlaylistManagerPage'));
const ChannelCustomizationPage = lazy(() => import('@/features/misc/pages/ChannelCustomizationPage'));
const VideoEditorPage = lazy(() => import('@/features/creator/pages/VideoEditorPage'));
const LoginPage = lazy(() => import('@/features/auth/pages/LoginPage'));
const RegisterPage = lazy(() => import('@/features/auth/pages/RegisterPage'));

// Layout components - using correct paths
const AccountLayout = lazy(() => import('@/components/AccountLayout'));
const StudioLayout = lazy(() => import('@/features/common/components/StudioLayout'));
const ProtectedRoute = lazy(() => import('@/features/auth/components/ProtectedRoute'));

// Route configuration for manual verification testing
export const mainRoutes: RouteObject[] = [
  {
    path: '/',
    element: <BasicLayout />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'watch/:videoId',
        element: <WatchPage />,
      },
      {
        path: 'watch',
        element: <WatchPage />,
      },
      {
        path: 'search',
        element: <SearchResultsPage />,
      },
      {
        path: 'trending',
        element: <TrendingPage />,
      },
      {
        path: 'shorts',
        element: <ShortsPage />,
      },
      {
        path: 'subscriptions',
        element: <SubscriptionsPage />,
      },
      {
        path: 'history',
        element: <HistoryPage />,
      },
      {
        path: 'watch-history',
        element: <HistoryPage />,
      },
      {
        path: 'playlists',
        element: <PlaylistsPage />,
      },
      {
        path: 'playlist/:playlistId',
        element: <PlaylistDetailPage />,
      },
      {
        path: 'watch-later',
        element: <WatchLaterPage />,
      },
      {
        path: 'liked-videos',
        element: <LikedVideosPage />,
      },
      {
        path: 'channel/:channelIdOrName',
        element: <ChannelPage />,
      },
      {
        path: 'user/:userName',
        element: <UserPage />,
      },
      {
        path: 'library',
        element: <LibraryPage />,
      },
      {
        path: 'your-data',
        element: <YourDataPage />,
      },
      {
        path: 'go-live',
        element: <GoLivePage />,
      },
      {
        path: 'ai-content-spark',
        element: <AIContentSparkPage />,
      },
      {
        path: 'upload',
        element: <VideoUploadPage />,
      },
      {
        path: 'settings',
        element: <SettingsPage />,
      },
      {
        path: 'admin',
        element: <AdminPage />,
      },
      {
        path: 'studio',
        element: <StudioPage />,
      },
      {
        path: 'studio/upload',
        element: <UploadPage />,
      },
      // Account section with nested routes and dedicated layout
      {
        path: 'account',
        element: <AccountLayout />,
        children: [
          {
            path: 'settings',
            element: <SettingsPage />,
          },
          {
            path: 'privacy',
            element: <SettingsPage />, // Can be replaced with dedicated privacy page later
          },
          {
            path: 'data',
            element: <YourDataPage />,
          },
        ],
      },
      {
        path: 'analytics',
        element: <AnalyticsPage />,
      },
      // Redirect for legacy content-manager route
      {
        path: 'content-manager',
        element: <ContentManagerPage />,
      },
    ],
  },
];

// Route configuration for studio
export const studioRoutes: RouteObject[] = [
  {
    path: 'studio',
    element: <StudioLayout />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: <StudioDashboardPage />,
      },
      {
        path: 'analytics',
        element: <AnalyticsPage />,
      },
      {
        path: 'comments',
        element: <CommentModerationPage />,
      },
      {
        path: 'monetization',
        element: <MonetizationPage />,
      },
      {
        path: 'content',
        element: <ContentManagerPage />,
      },
      {
        path: 'creator',
        element: <CreatorStudioPage />,
      },
      {
        path: 'community',
        element: <CommunityPage />,
      },
      {
        path: 'playlists',
        element: <PlaylistManagerPage />,
      },
      {
        path: 'customization',
        element: <ChannelCustomizationPage />,
      },
      {
        path: 'editor',
        element: <VideoEditorPage />,
      },
    ],
  },
];

// Authentication routes (outside main layout)
const authRoutes: RouteObject[] = [
  {
    path: '/login',
    element: (
      <ProtectedRoute requireAuth={false}>
        <LoginPage />
      </ProtectedRoute>
    ),
    errorElement: <ErrorBoundary />,
  },
  {
    path: '/register',
    element: (
      <ProtectedRoute requireAuth={false}>
        <RegisterPage />
      </ProtectedRoute>
    ),
    errorElement: <ErrorBoundary />,
  },
];

// Combined route configuration
export const routes: RouteObject[] = [...mainRoutes, ...studioRoutes, ...authRoutes];
