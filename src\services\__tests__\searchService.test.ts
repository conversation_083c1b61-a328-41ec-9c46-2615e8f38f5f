/**
 * Search Service Unit Tests
 * Tests for centralized search functionality
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';

import { searchService } from '../searchService';

import type { Video } from '../../types/video';

// Mock the dependencies
vi.mock('../unifiedDataService', () => ({
  unifiedDataService: {
    searchVideos: vi.fn(),
  },
}));

vi.mock('../api/base', () => ({
  api: {
    get: vi.fn(),
    delete: vi.fn(),
    upload: vi.fn(),
  },
}));

vi.mock('../adapters/videoAdapter', () => ({
  unifiedVideosToVideos: vi.fn((videos) => videos),
}));

const mockVideo: Video = {
  id: 'test-video-id',
  title: 'Test Video',
  description: 'Test Description',
  thumbnailUrl: 'https://example.com/thumb.jpg',
  videoUrl: 'https://example.com/video.mp4',
  duration: '5:30',
  viewCount: 1000,
  views: '1K views',
  likeCount: 50,
  likes: 50,
  dislikeCount: 2,
  dislikes: 2,
  commentCount: 10,
  publishedAt: '2023-01-01T00:00:00Z',
  uploadedAt: '2023-01-01T00:00:00Z',
  channelId: 'test-channel',
  channelName: 'Test Channel',
  channelAvatarUrl: 'https://example.com/avatar.jpg',
  channelTitle: 'Test Channel',
  category: 'Entertainment',
  tags: ['test'],
  isLive: false,
  isShort: false,
  visibility: 'public',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  channel: {
    id: 'test-channel',
    name: 'Test Channel',
    avatarUrl: 'https://example.com/avatar.jpg',
    isVerified: false,
    subscribers: 1000,
    subscriberCount: '1K',
  },
};

describe('SearchService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('searchVideos', () => {
    it('should return videos from unified data service', async () => {
      const { unifiedDataService } = await import('../unifiedDataService');
      const { unifiedVideosToVideos } = await import('../adapters/videoAdapter');

      vi.mocked(unifiedDataService.searchVideos).mockResolvedValue({
        data: [mockVideo] as any,
        sources: { local: { count: 1, hasMore: false }, youtube: { count: 0, hasMore: false } },
        totalCount: 1,
        hasMore: false,
      });

      vi.mocked(unifiedVideosToVideos).mockReturnValue([mockVideo]);

      const result = await searchService.searchVideos('test query');

      expect(result).toEqual([mockVideo]);
      expect(unifiedDataService.searchVideos).toHaveBeenCalledWith(
        'test query',
        {
          type: 'video',
          category: undefined,
          duration: undefined,
          uploadDate: undefined,
          sortBy: undefined,
        },
        20,
      );
    });

    it('should handle search errors gracefully', async () => {
      const { unifiedDataService } = await import('../unifiedDataService');

      vi.mocked(unifiedDataService.searchVideos).mockRejectedValue(new Error('Search failed'));

      const result = await searchService.searchVideos('test query');

      expect(result).toEqual([]);
    });

    it('should convert shorts filter correctly', async () => {
      const { unifiedDataService } = await import('../unifiedDataService');

      vi.mocked(unifiedDataService.searchVideos).mockResolvedValue({
        data: [],
        sources: { local: { count: 0, hasMore: false }, youtube: { count: 0, hasMore: false } },
        totalCount: 0,
        hasMore: false,
      });

      await searchService.searchVideos('test', { type: 'shorts' });

      expect(unifiedDataService.searchVideos).toHaveBeenCalledWith(
        'test',
        expect.objectContaining({ type: 'short' }),
        20,
      );
    });
  });

  describe('getSearchSuggestions', () => {
    it('should return search suggestions from API', async () => {
      const { api } = await import('../api/base');
      const mockSuggestions = [
        { query: 'test suggestion', type: 'query' as const },
        { query: 'another test', type: 'trending' as const },
      ];

      vi.mocked(api.get).mockResolvedValue({ data: mockSuggestions });

      const result = await searchService.getSearchSuggestions('test');

      expect(result).toEqual(mockSuggestions);
      expect(api.get).toHaveBeenCalledWith('/api/search/suggestions', {
        q: 'test',
        limit: 10,
      });
    });

    it('should handle API errors gracefully', async () => {
      const { api } = await import('../api/base');

      vi.mocked(api.get).mockRejectedValue(new Error('API Error'));

      const result = await searchService.getSearchSuggestions('test');

      expect(result).toEqual([]);
    });
  });

  describe('getTrendingSearches', () => {
    it('should return trending searches from API', async () => {
      const { api } = await import('../api/base');
      const mockTrending = ['trending query 1', 'trending query 2'];

      vi.mocked(api.get).mockResolvedValue({ data: mockTrending });

      const result = await searchService.getTrendingSearches();

      expect(result).toEqual(mockTrending);
      expect(api.get).toHaveBeenCalledWith('/api/search/trending', {
        region: undefined,
        limit: 20,
      });
    });
  });

  describe('advancedSearch', () => {
    it('should perform advanced search and return results with timing', async () => {
      const { unifiedDataService } = await import('../unifiedDataService');
      const { unifiedVideosToVideos } = await import('../adapters/videoAdapter');

      // Add small delay to ensure measurable time
      vi.mocked(unifiedDataService.searchVideos).mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 1));
        return {
          data: [mockVideo] as any,
          sources: { local: { count: 1, hasMore: false }, youtube: { count: 0, hasMore: false } },
          totalCount: 1,
          hasMore: false,
        };
      });

      vi.mocked(unifiedVideosToVideos).mockReturnValue([mockVideo]);

      const result = await searchService.advancedSearch('test query');

      expect(result.videos).toEqual([mockVideo]);
      expect(result.totalResults).toBe(1);
      expect(result.searchTime).toBeGreaterThanOrEqual(0);
      expect(result.relatedQueries).toEqual([]);
    });

    it('should handle search failures in advanced search', async () => {
      const { unifiedDataService } = await import('../unifiedDataService');

      vi.mocked(unifiedDataService.searchVideos).mockRejectedValue(new Error('Search failed'));

      const result = await searchService.advancedSearch('test query');

      expect(result.videos).toEqual([]);
      expect(result.totalResults).toBe(0);
      expect(result.searchTime).toBeGreaterThan(0);
    });
  });

  describe('searchByImage', () => {
    it('should perform image search using API upload', async () => {
      const { api } = await import('../api/base');
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });

      vi.mocked(api.upload).mockResolvedValue({ data: [mockVideo] });

      const result = await searchService.searchByImage(mockFile);

      expect(result).toEqual([mockVideo]);
      expect(api.upload).toHaveBeenCalledWith('/api/search/image', mockFile, {});
    });
  });

  describe('clearSearchHistory', () => {
    it('should clear search history via API', async () => {
      const { api } = await import('../api/base');

      vi.mocked(api.delete).mockResolvedValue({ data: null });

      await searchService.clearSearchHistory();

      expect(api.delete).toHaveBeenCalledWith('/api/search/history');
    });

    it('should handle API errors when clearing history', async () => {
      const { api } = await import('../api/base');

      vi.mocked(api.delete).mockRejectedValue(new Error('Delete failed'));

      // Should not throw
      await expect(searchService.clearSearchHistory()).resolves.toBeUndefined();
    });
  });
});
