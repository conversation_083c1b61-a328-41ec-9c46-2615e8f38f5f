import React from 'react';
import { Link } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { formatDuration, formatViewCount } from '@/utils/format';
import { Video } from '@/types/video';
import { cn } from '@/utils/cn';

export interface UnifiedVideoCardProps {
  video: Video;
  className?: string;
  showChannel?: boolean;
  showDescription?: boolean;
  layout?: 'grid' | 'list';
  onClick?: () => void;
}

/**
 * Unified video card component that displays video information
 * Supports both grid and list layouts
 */
export const UnifiedVideoCard: React.FC<UnifiedVideoCardProps> = ({
  video,
  className,
  showChannel = true,
  showDescription = false,
  layout = 'grid',
  onClick
}) => {
  const isListLayout = layout === 'list';
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <div
      className={cn(
        'group cursor-pointer transition-all duration-200 hover:scale-[1.02]',
        isListLayout ? 'flex gap-4' : 'flex flex-col',
        className
      )}
      onClick={handleClick}
    >
      {/* Thumbnail */}
      <div className={cn(
        'relative overflow-hidden rounded-lg bg-gray-200',
        isListLayout ? 'w-48 h-28 flex-shrink-0' : 'w-full aspect-video'
      )}>
        <Link to={`/watch?v=${video.id}`}>
          <img
            src={video.thumbnail}
            alt={video.title}
            className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
            loading="lazy"
          />
          {video.duration && (
            <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-1.5 py-0.5 rounded">
              {formatDuration(video.duration)}
            </div>
          )}
        </Link>
      </div>

      {/* Content */}
      <div className={cn(
        'flex gap-3',
        isListLayout ? 'flex-1' : 'mt-3'
      )}>
        {/* Channel Avatar */}
        {showChannel && !isListLayout && (
          <div className="flex-shrink-0">
            <Link to={`/channel/${video.channelId}`}>
              <img
                src={video.channelAvatar || '/default-avatar.png'}
                alt={video.channelName}
                className="w-9 h-9 rounded-full"
              />
            </Link>
          </div>
        )}

        {/* Video Info */}
        <div className="flex-1 min-w-0">
          <Link to={`/watch?v=${video.id}`}>
            <h3 className={cn(
              'font-medium text-gray-900 dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors',
              isListLayout ? 'text-base' : 'text-sm'
            )}>
              {video.title}
            </h3>
          </Link>

          {showChannel && (
            <Link 
              to={`/channel/${video.channelId}`}
              className="block mt-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              {video.channelName}
            </Link>
          )}

          <div className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            <span>{formatViewCount(video.viewCount)} views</span>
            {video.publishedAt && (
              <>
                <span className="mx-1">•</span>
                <span>{formatDistanceToNow(new Date(video.publishedAt), { addSuffix: true })}</span>
              </>
            )}
          </div>

          {showDescription && video.description && (
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {video.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default UnifiedVideoCard;