import React from 'react';

interface ShortsIconProps {
  className?: string;
  size?: number;
}

export default function ShortsIcon({ className = '', size = 24 }: ShortsIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M10 12l8-5v10l-8-5z"
        fill="currentColor"
      />
      <rect
        x="6"
        y="3"
        width="2"
        height="18"
        rx="1"
        fill="currentColor"
      />
    </svg>
  );
}
