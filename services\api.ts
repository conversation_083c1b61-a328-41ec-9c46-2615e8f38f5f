import { Video } from '../src/types';

export interface SearchFilters {
  category?: string;
  duration?: 'any' | 'short' | 'medium' | 'long';
  uploadDate?: 'any' | 'hour' | 'today' | 'week' | 'month' | 'year';
  sortBy?: 'relevance' | 'date' | 'views' | 'rating';
}

export interface SearchResponse {
  videos: Video[];
  totalCount: number;
  hasMore: boolean;
  nextPageToken?: string;
}

export class VideoService {
  private static baseUrl = '/api/videos';

  static async searchVideos(
    query: string, 
    filters: SearchFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<SearchResponse> {
    try {
      const params = new URLSearchParams({
        q: query,
        page: page.toString(),
        limit: limit.toString(),
        ...filters
      });

      const response = await fetch(`${this.baseUrl}/search?${params}`);
      
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Video search error:', error);
      // Return mock data for development
      return this.getMockSearchResults(query, filters, page, limit);
    }
  }

  static async getVideo(id: string): Promise<Video | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error(`Failed to fetch video: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Get video error:', error);
      return null;
    }
  }

  static async getTrendingVideos(
    category?: string,
    limit: number = 20
  ): Promise<Video[]> {
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        ...(category && { category })
      });

      const response = await fetch(`${this.baseUrl}/trending?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch trending videos: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Get trending videos error:', error);
      return [];
    }
  }

  private static getMockSearchResults(
    query: string,
    filters: SearchFilters,
    page: number,
    limit: number
  ): SearchResponse {
    // Mock implementation for development
    const mockVideos: Video[] = Array.from({ length: limit }, (_, i) => ({
      id: `search-${page}-${i}`,
      title: `${query} - Video ${page * limit + i + 1}`,
      description: `Mock search result for "${query}"`,
      thumbnailUrl: `https://via.placeholder.com/320x180?text=Video+${i + 1}`,
      videoUrl: `/videos/mock-${i}.mp4`,
      duration: Math.floor(Math.random() * 600) + 60,
      views: `${Math.floor(Math.random() * 1000000)}`,
      viewCount: Math.floor(Math.random() * 1000000),
      likes: Math.floor(Math.random() * 10000),
      likeCount: Math.floor(Math.random() * 10000),
      dislikes: Math.floor(Math.random() * 100),
      dislikeCount: Math.floor(Math.random() * 100),
      commentCount: Math.floor(Math.random() * 1000),
      channelId: `channel-${i % 5}`,
      channelName: `Channel ${i % 5 + 1}`,
      channelAvatarUrl: `https://via.placeholder.com/50x50?text=C${i % 5 + 1}`,
      publishedAt: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString(),
      uploadedAt: `${Math.floor(Math.random() * 30) + 1} days ago`,
      category: filters.category || 'Entertainment',
      tags: ['mock', 'test', 'video'],
      language: 'en',
      isShort: false,
      visibility: 'public' as const,
      status: 'published' as const,
      source: 'local' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));

    return {
      videos: mockVideos,
      totalCount: 1000,
      hasMore: page * limit < 1000,
      nextPageToken: `page-${page + 1}`
    };
  }
}
