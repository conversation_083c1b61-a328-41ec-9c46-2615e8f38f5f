import React, { createContext, useContext, useState, useEffect } from 'react';
import type { Video } from '@/types';
import { 
  getWatchLaterVideos, 
  addToWatchLater as addToWatchLaterService,
  removeFromWatchLater as removeFromWatchLaterService 
} from '../services/mockVideoService';

interface WatchLaterContextType {
  videos: Video[];
  loading: boolean;
  error: string | null;
  addToWatchLater: (videoId: string) => Promise<void>;
  removeFromWatchLater: (videoId: string) => Promise<void>;
  isInWatchLater: (videoId: string) => boolean;
  refreshWatchLater: () => Promise<void>;
}

const WatchLaterContext = createContext<WatchLaterContextType | undefined>(undefined);

interface WatchLaterProviderProps {
  children: React.ReactNode;
}

export const WatchLaterProvider: React.FC<WatchLaterProviderProps> = ({ children }) => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshWatchLater = async () => {
    try {
      setLoading(true);
      setError(null);
      const watchLaterVideos = await getWatchLaterVideos();
      setVideos(watchLaterVideos);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load watch later videos');
    } finally {
      setLoading(false);
    }
  };

  const addToWatchLater = async (videoId: string) => {
    try {
      setError(null);
      const success = await addToWatchLaterService(videoId);
      if (success) {
        await refreshWatchLater();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add video to watch later');
    }
  };

  const removeFromWatchLater = async (videoId: string) => {
    try {
      setError(null);
      const success = await removeFromWatchLaterService(videoId);
      if (success) {
        setVideos(prev => prev.filter(video => video.id !== videoId));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove video from watch later');
    }
  };

  const isInWatchLater = (videoId: string) => {
    return videos.some(video => video.id === videoId);
  };

  useEffect(() => {
    refreshWatchLater();
  }, []);

  return (
    <WatchLaterContext.Provider value={{
      videos,
      loading,
      error,
      addToWatchLater,
      removeFromWatchLater,
      isInWatchLater,
      refreshWatchLater,
    }}>
      {children}
    </WatchLaterContext.Provider>
  );
};

export const useWatchLater = () => {
  const context = useContext(WatchLaterContext);
  if (context === undefined) {
    throw new Error('useWatchLater must be used within a WatchLaterProvider');
  }
  return context;
};

export default WatchLaterContext;
