// User feature components
export { default as HistoryIcon } from '../components/icons/HistoryIcon';
export { default as PlaylistIcon } from '../components/icons/PlaylistIcon';
export { default as SubscriptionsIcon } from '../components/icons/SubscriptionsIcon';
export { default as HistoryPageSkeleton } from '../components/LoadingStates/HistoryPageSkeleton';
export { default as LikedVideosPageSkeleton } from '../components/LoadingStates/LikedVideosPageSkeleton';

// Re-export common components that might be used
export { VideoCard } from '@/components/VideoCard';
export { LoadingSpinner } from '@/components/ui/LoadingSpinner';
export { Button } from '@/components/ui/Button';

// User-specific components
export interface UserComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Placeholder components for missing ones
export const UserProfile: React.FC<UserComponentProps> = ({ className, children }) => (
  <div className={className}>{children}</div>
);

export const UserStats: React.FC<UserComponentProps> = ({ className, children }) => (
  <div className={className}>{children}</div>
);

export const UserSettings: React.FC<UserComponentProps> = ({ className, children }) => (
  <div className={className}>{children}</div>
);
