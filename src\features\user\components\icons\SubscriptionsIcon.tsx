import React from 'react';

interface SubscriptionsIconProps {
  className?: string;
  size?: number;
}

export const SubscriptionsIcon: React.FC<SubscriptionsIconProps> = ({ 
  className = '', 
  size = 24 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M20 8H4V6H20V8ZM18 2H6V4H18V2ZM22 12V20C22 21.1 21.1 22 20 22H4C2.9 22 2 21.1 2 20V12C2 10.9 2.9 10 4 10H20C21.1 10 22 10.9 22 12ZM15 16L10 13V19L15 16Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default SubscriptionsIcon;
