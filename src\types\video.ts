/**
 * Unified Video Domain Types
 *
 * This file contains the single source of truth for video-related types,
 * consolidating all scattered video definitions into one unified interface.
 */

// Base entity interface
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// Supporting types for Video
export interface VideoChannel {
  id: string;
  name: string;
  avatarUrl?: string;
  subscribers?: number;
  subscriberCount?: string;
  isVerified?: boolean;
  handle?: string;
}

export interface VideoStatistics {
  viewCount: number;
  likeCount: number;
  dislikeCount: number;
  favoriteCount: number;
  commentCount: number;
}

export interface VideoContentDetails {
  duration: string;
  dimension: string;
  definition: string;
  caption: string;
  licensedContent: boolean;
  regionRestriction?: {
    allowed?: string[];
    blocked?: string[];
  };
  contentRating?: Record<string, string>;
  projection?: string;
  hasCustomThumbnail?: boolean;
}

export interface VideoTopicDetails {
  topicIds: string[];
  relevantTopicIds: string[];
  topicCategories: string[];
}

export interface VideoStatus {
  uploadStatus: string;
  failureReason?: string;
  rejectionReason?: string;
  privacyStatus: string;
  publishAt?: string;
  license: string;
  embeddable: boolean;
  publicStatsViewable: boolean;
  madeForKids: boolean;
  selfDeclaredMadeForKids?: boolean;
}

export interface VideoLiveStreamingDetails {
  actualStartTime?: string;
  actualEndTime?: string;
  scheduledStartTime?: string;
  scheduledEndTime?: string;
  concurrentViewers?: string;
  activeLiveChatId?: string;
}

export interface VideoCaption {
  id: string;
  language: {
    code: string;
    name: string;
  };
  label: string;
  url: string;
  isAutoGenerated?: boolean;
}

export interface VideoSubtitle {
  language: string;
  languageCode: string;
  label: string;
  url: string;
  src?: string;
  srcLang?: string;
  isDefault: boolean;
}

export interface VideoQuality {
  name: string;
  value: string;
  resolution: string;
  bitrate: string;
}

export interface VideoChapter {
  start: number;
  title: string;
  thumbnail?: string;
}

// Legacy interface for backward compatibility
export interface Subtitle extends VideoSubtitle {}
export interface Chapter extends VideoChapter {}

export interface VideoMetadata {
  defaultLanguage?: string;
  uploadLocation?: string;
  recordingDate?: string;
  actualStartTime?: string;
  actualEndTime?: string;
  scheduledStartTime?: string;
  concurrentViewers?: string;
  embeddable?: boolean;
  publicStatsViewable?: boolean;
  madeForKids?: boolean;
  selfDeclaredMadeForKids?: boolean;
}

export type VideoVisibility = 'public' | 'unlisted' | 'private' | 'scheduled';

/**
 * Unified Video Interface
 *
 * This is the single source of truth for video data across the entire application.
 * It includes all fields that might be needed by any component or service.
 */
export interface Video extends BaseEntity {
  // Core identification
  id: string;
  title?: string;
  description?: string;

  // Media assets
  thumbnailUrl?: string;
  videoUrl?: string;

  // Duration - supporting both string and number formats
  duration?: string | number;

  // View metrics (supporting legacy naming)
  views?: string; // formatted view count like "1.2M views"
  viewCount?: number; // numeric view count

  // Engagement metrics
  likes?: number;
  likeCount?: number; // primary field
  dislikes?: number;
  dislikeCount?: number; // primary field
  commentCount?: number;

  // Temporal data
  publishedAt?: string;
  uploadedAt?: string;

  // Channel information
  channelId?: string;
  channelName?: string;
  channelAvatarUrl?: string;
  channelTitle?: string; // alias for channelName
  channelThumbnail?: string; // alias for channelAvatarUrl
  channel?: VideoChannel; // nested channel object

  // Content metadata
  category?: string;
  tags?: string[];

  // Content type flags
  isLive?: boolean;
  isShort?: boolean;
  isUpcoming?: boolean;

  // User interaction states
  isLiked?: boolean;
  isDisliked?: boolean;
  isSaved?: boolean;
  isHearted?: boolean;
  isPinned?: boolean;
  isEdited?: boolean;

  // Visibility and privacy
  visibility?: VideoVisibility;
  privacyStatus?: 'public' | 'private' | 'unlisted' | 'scheduled';

  // Content safety and restrictions
  isFamilySafe?: boolean;
  allowedRegions?: string[];
  blockedRegions?: string[];
  isAgeRestricted?: boolean;
  embeddable?: boolean;

  // Live streaming specific
  scheduledStartTime?: string;
  concurrentViewers?: number;
  isLiveContent?: boolean;
  viewerCount?: string;

  // Technical details
  recordingStatus?: 'none' | 'recorded' | 'not_recorded';
  uploadStatus?: 'deleted' | 'failed' | 'processed' | 'rejected' | 'uploaded';
  defaultLanguage?: string;
  defaultAudioLanguage?: string;
  definition?: string;
  license?: string;
  publishAt?: string;
  selfDeclaredMadeForKids?: boolean;

  // Structured data
  statistics?: VideoStatistics;
  contentDetails?: VideoContentDetails;
  topicDetails?: VideoTopicDetails;
  status?: VideoStatus;
  liveStreamingDetails?: VideoLiveStreamingDetails;
  captions?: VideoCaption[];
  subtitles?: VideoSubtitle[];
  chapters?: VideoChapter[];
  metadata?: VideoMetadata;

  // Legacy YouTube API fields for backward compatibility
  player?: {
    embedHtml: string;
    embedHeight?: number;
    embedWidth?: number;
  };
  fileDetails?: {
    fileName?: string;
    fileSize?: string;
    fileType?: string;
    container?: string;
    videoStreams?: Array<{
      widthPixels?: number;
      heightPixels?: number;
      frameRateFps?: number;
      aspectRatio?: number;
      codec?: string;
      bitrateBps?: string;
      rotation?: string;
      vendor?: string;
    }>;
    audioStreams?: Array<{
      channelCount?: number;
      codec?: string;
      bitrateBps?: string;
      vendor?: string;
    }>;
    durationMs?: string;
    bitrateBps?: string;
    creationTime?: string;
  };
  processingDetails?: {
    processingStatus?: string;
    processingProgress?: {
      partsTotal?: string;
      partsProcessed?: string;
      timeLeftMs?: string;
    };
    processingFailureReason?: string;
    fileDetailsAvailability?: string;
    processingIssuesAvailability?: string;
    tagSuggestionsAvailability?: string;
    editorSuggestionsAvailability?: string;
    thumbnailsAvailability?: string;
  };
  suggestions?: {
    processingErrors?: string[];
    processingWarnings?: string[];
    processingHints?: string[];
    tagSuggestions?: Array<{
      tag: string;
      categoryRestricts: string[];
    }>;
    editorSuggestions?: string[];
  };
  localizations?: Record<string, {
    title: string;
    description: string;
  }>;

  // UI state (for components)
  nextPageToken?: string;
  relatedVideos?: Video[];
  saveModalLoading?: boolean;
  showAllRelated?: boolean;
  saveButtonRef?: React.RefObject<HTMLButtonElement>;
  saveModalRef?: React.RefObject<HTMLDivElement>;

  // Legacy compatibility fields
  thumbnail?: string; // alias for thumbnailUrl
  channelAvatar?: string; // alias for channelAvatarUrl

  // For Shorts-specific properties when isShort is true
  isVertical?: boolean;
  musicInfo?: {
    title?: string;
    artist?: string;
    coverUrl?: string;
  };
  shares?: number;
  saves?: number;
  hasCaptions?: boolean;
  hasAudio?: boolean;
  viewDuration?: number;
  swipeAwayRate?: number;
  isMonetized?: boolean;
  hasInteractiveElements?: boolean;
}

/**
 * External API Video Response Types
 * These interfaces map to external API responses before normalization
 */

// YouTube API Video Response
export interface ApiYouTubeVideo {
  id: string;
  snippet: {
    title: string;
    description: string;
    thumbnails: {
      default?: { url: string; width: number; height: number };
      medium?: { url: string; width: number; height: number };
      high?: { url: string; width: number; height: number };
      standard?: { url: string; width: number; height: number };
      maxres?: { url: string; width: number; height: number };
    };
    publishedAt: string;
    channelId: string;
    channelTitle: string;
    tags?: string[];
    categoryId: string;
    defaultLanguage?: string;
    liveBroadcastContent?: 'none' | 'upcoming' | 'live';
  };
  statistics: {
    viewCount: string;
    likeCount: string;
    dislikeCount?: string;
    favoriteCount?: string;
    commentCount: string;
  };
  contentDetails: {
    duration: string; // ISO 8601 format (PT4M13S)
    dimension: string;
    definition: string;
    caption: string;
    licensedContent?: boolean;
    regionRestriction?: {
      allowed?: string[];
      blocked?: string[];
    };
    contentRating?: Record<string, string>;
    projection?: string;
  };
  status?: {
    uploadStatus: string;
    failureReason?: string;
    rejectionReason?: string;
    privacyStatus: string;
    publishAt?: string;
    license: string;
    embeddable: boolean;
    publicStatsViewable: boolean;
    madeForKids: boolean;
    selfDeclaredMadeForKids?: boolean;
  };
  liveStreamingDetails?: {
    actualStartTime?: string;
    actualEndTime?: string;
    scheduledStartTime?: string;
    scheduledEndTime?: string;
    concurrentViewers?: string;
    activeLiveChatId?: string;
  };
  localizations?: Record<string, {
    title: string;
    description: string;
  }>;
  topicDetails?: {
    topicIds?: string[];
    relevantTopicIds?: string[];
    topicCategories?: string[];
  };
}

// Generic external API video (for other services)
export interface ApiExternalVideo {
  id: string;
  title: string;
  description?: string;
  thumbnail_url?: string;
  url?: string;
  duration?: number | string;
  view_count?: number | string;
  like_count?: number;
  created_at?: string;
  updated_at?: string;
  channel?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
  [key: string]: unknown; // Allow additional properties
}

// Union type for all external API video responses
export type ApiVideo = ApiYouTubeVideo | ApiExternalVideo;

/**
 * Supporting types that may be used after consolidation
 */

export interface Channel extends BaseEntity {
  name?: string;
  handle?: string;
  description?: string;
  avatarUrl?: string;
  banner?: string;
  subscribers?: number;
  subscriberCount?: string;
  videoCount?: number;
  totalViews?: number;
  isVerified?: boolean;
  joinedDate?: string;
  country?: string;
}

export interface Comment extends BaseEntity {
  content: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    isChannelOwner: boolean;
    isVerified: boolean;
  };
  videoId: string;
  parentId?: string;
  likes: number;
  dislikes: number;
  replies: Comment[];
  isEdited: boolean;
  isPinned: boolean;
  isHearted: boolean;
  userAvatarUrl: string;
  userName: string;
  commentText: string;
  timestamp: string;
  isLikedByCurrentUser: boolean;
  isDislikedByCurrentUser: boolean;
  replyCount: number;
  replyTo?: string;
  authorId: string;
  authorName: string;
  authorAvatar: string;
}

// Analytics slice that may be used across the app
export interface AnalyticsSlice {
  totalViews: number;
  uniqueViews: number;
  averageWatchTime: number;
  clickThroughRate: number;
  engagement: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
    subscribersGained: number;
  };
  demographics: {
    ageGroups: Record<string, number>;
    genders: Record<string, number>;
    countries: Record<string, number>;
  };
  trafficSources: Array<{
    source: string;
    views: number;
    percentage: number;
  }>;
}

// Export the main Video type as default
export default Video;
