/**
 * Video Adapter Unit Tests
 * Tests for converting between UnifiedVideoMetadata and Video types
 */

import { describe, it, expect } from 'vitest';

import { unifiedVideoToVideo, unifiedVideosToVideos } from '../videoAdapter';

import type { UnifiedVideoMetadata } from '../../metadataNormalizationService';

describe('Video Adapter', () => {
  const mockUnifiedVideo: UnifiedVideoMetadata = {
    id: 'test-video-id',
    title: 'Test Video Title',
    description: 'Test video description',
    thumbnailUrl: 'https://example.com/thumbnail.jpg',
    videoUrl: 'https://example.com/video.mp4',
    views: 1000,
    viewsFormatted: '1K views',
    likes: 100,
    dislikes: 5,
    commentCount: 50,
    channel: {
      id: 'test-channel-id',
      name: 'Test Channel',
      avatarUrl: 'https://example.com/avatar.jpg',
      subscribers: 10000,
      subscribersFormatted: '10K subscribers',
      isVerified: true,
    },
    duration: '3:45',
    publishedAt: '2023-01-01T00:00:00Z',
    publishedAtFormatted: '1 year ago',
    category: 'Entertainment',
    tags: ['test', 'video'],
    isLive: false,
    isShort: false,
    visibility: 'public',
    source: 'youtube' as const,
    metadata: {
      quality: 'hd',
      definition: 'hd',
      captions: false,
      language: 'en',
      license: 'youtube',
    },
  };

  describe('unifiedVideoToVideo', () => {
    it('should convert UnifiedVideoMetadata to Video type correctly', () => {
      const result = unifiedVideoToVideo(mockUnifiedVideo);

      expect(result).toMatchObject({
        id: 'test-video-id',
        title: 'Test Video Title',
        description: 'Test video description',
        thumbnailUrl: 'https://example.com/thumbnail.jpg',
        videoUrl: 'https://example.com/video.mp4',
        duration: '3:45',
        viewCount: 1000,
        views: '1K views',
        likeCount: 100,
        likes: 100,
        dislikeCount: 5,
        dislikes: 5,
        commentCount: 50,
        publishedAt: '2023-01-01T00:00:00Z',
        uploadedAt: '2023-01-01T00:00:00Z',
        channelId: 'test-channel-id',
        channelName: 'Test Channel',
        channelAvatarUrl: 'https://example.com/avatar.jpg',
        channelTitle: 'Test Channel',
        category: 'Entertainment',
        tags: ['test', 'video'],
        isLive: false,
        isShort: false,
        visibility: 'public',
      });

      expect(result.channel).toMatchObject({
        id: 'test-channel-id',
        name: 'Test Channel',
        avatarUrl: 'https://example.com/avatar.jpg',
        isVerified: true,
        subscribers: 10000,
        subscriberCount: '10K subscribers',
      });

      expect(result.createdAt).toBe('2023-01-01T00:00:00Z');
      expect(result.updatedAt).toBeDefined();
      expect(new Date(result.updatedAt).getTime()).toBeGreaterThan(0);
    });

    it('should handle missing optional fields gracefully', () => {
      const minimalVideo: UnifiedVideoMetadata = {
        id: 'minimal-video',
        title: 'Minimal Video',
        description: '',
        thumbnailUrl: '',
        views: 0,
        viewsFormatted: '0 views',
        likes: 0,
        dislikes: 0,
        commentCount: 0,
        channel: {
          id: 'minimal-channel',
          name: 'Minimal Channel',
          avatarUrl: '',
          subscribers: 0,
          subscribersFormatted: '0 subscribers',
          isVerified: false,
        },
        duration: '0:00',
        publishedAt: '2023-01-01T00:00:00Z',
        publishedAtFormatted: 'Just now',
        category: 'Other',
        tags: [],
        isLive: false,
        isShort: false,
        visibility: 'public',
        source: 'local' as const,
        metadata: {
          quality: 'sd',
          definition: 'sd',
          captions: false,
          language: 'en',
          license: 'standard',
        },
      };

      const result = unifiedVideoToVideo(minimalVideo);

      expect(result.id).toBe('minimal-video');
      expect(result.title).toBe('Minimal Video');
      expect(result.tags).toEqual([]);
      expect(result.channel?.subscribers).toBe(0);
    });
  });

  describe('unifiedVideosToVideos', () => {
    it('should convert array of UnifiedVideoMetadata to Video array', () => {
      const mockVideos = [mockUnifiedVideo, { ...mockUnifiedVideo, id: 'second-video' }];
      const result = unifiedVideosToVideos(mockVideos);

      expect(result).toHaveLength(2);
      expect(result[0]?.id).toBe('test-video-id');
      expect(result[1]?.id).toBe('second-video');
    });

    it('should handle empty array', () => {
      const result = unifiedVideosToVideos([]);
      expect(result).toEqual([]);
    });

    it('should handle array with one video', () => {
      const result = unifiedVideosToVideos([mockUnifiedVideo]);
      expect(result).toHaveLength(1);
      expect(result[0]?.id).toBe('test-video-id');
    });
  });
});
