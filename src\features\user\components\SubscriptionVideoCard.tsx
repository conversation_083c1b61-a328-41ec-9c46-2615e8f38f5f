import React from 'react';
import type { Video } from '@/types';

interface SubscriptionVideoCardProps {
  video: Video;
  onVideoClick?: (video: Video) => void;
  className?: string;
}

export const SubscriptionVideoCard: React.FC<SubscriptionVideoCardProps> = ({
  video,
  onVideoClick,
  className = '',
}) => {
  const handleClick = () => {
    onVideoClick?.(video);
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer ${className}`}
      onClick={handleClick}
    >
      <div className="relative">
        <img
          src={video.thumbnailUrl}
          alt={video.title}
          className="w-full h-48 object-cover rounded-t-lg"
        />
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          {video.duration}
        </div>
      </div>
      
      <div className="p-4">
        <div className="flex items-start space-x-3">
          <img
            src={video.channelAvatarUrl}
            alt={video.channelName}
            className="w-10 h-10 rounded-full flex-shrink-0"
          />
          
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-gray-900 line-clamp-2 mb-1">
              {video.title}
            </h3>
            
            <div className="flex items-center space-x-1 text-sm text-gray-600">
              <span>{video.channelName}</span>
              {video.isVerified && (
                <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            
            <div className="text-sm text-gray-500 mt-1">
              <span>{video.views} views</span>
              <span className="mx-1">•</span>
              <span>{new Date(video.publishedAt).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionVideoCard;
