import React from 'react';
import { Video } from '../src/types';

export interface OptimizedVideoCardProps {
  video: Video;
  onVideoClick?: (video: Video) => void;
  onChannelClick?: (channelId: string) => void;
  onMoreClick?: () => void;
  showChannel?: boolean;
  lazy?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'compact';
  className?: string;
  [key: string]: any;
}

export default function OptimizedVideoCard({
  video,
  onVideoClick,
  onChannelClick,
  onMoreClick,
  showChannel = true,
  lazy = false,
  size = 'md',
  variant = 'default',
  className = '',
  ...props
}: OptimizedVideoCardProps) {
  const handleVideoClick = () => {
    onVideoClick?.(video);
  };

  const handleChannelClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChannelClick?.(video.channelId);
  };

  const sizeClasses = {
    sm: 'w-48',
    md: 'w-64',
    lg: 'w-80'
  };

  const variantClasses = {
    default: 'space-y-2',
    compact: 'flex space-x-3'
  };

  return (
    <div 
      className={`cursor-pointer hover:scale-105 transition-transform ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
      onClick={handleVideoClick}
      {...props}
    >
      {/* Thumbnail */}
      <div className="relative">
        <img
          src={video.thumbnailUrl || '/default-thumbnail.jpg'}
          alt={video.title || 'Video thumbnail'}
          className="w-full aspect-video object-cover rounded-lg"
          loading={lazy ? 'lazy' : 'eager'}
        />
        {video.duration && (
          <span className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-1 py-0.5 rounded">
            {typeof video.duration === 'number' 
              ? `${Math.floor(video.duration / 60)}:${(video.duration % 60).toString().padStart(2, '0')}`
              : video.duration
            }
          </span>
        )}
      </div>

      {/* Video Info */}
      <div className={variant === 'compact' ? 'flex-1' : ''}>
        <h3 className="font-medium text-sm line-clamp-2 mb-1">
          {video.title || 'Untitled Video'}
        </h3>
        
        {showChannel && (
          <div 
            className="flex items-center space-x-2 text-xs text-gray-600 mb-1 cursor-pointer hover:text-gray-800"
            onClick={handleChannelClick}
          >
            <img
              src={video.channelAvatarUrl || '/default-avatar.jpg'}
              alt={video.channelName || 'Channel'}
              className="w-6 h-6 rounded-full"
            />
            <span>{video.channelName || 'Unknown Channel'}</span>
          </div>
        )}

        <div className="text-xs text-gray-500">
          <span>{video.views || '0'} views</span>
          {video.uploadedAt && (
            <>
              <span className="mx-1">•</span>
              <span>{video.uploadedAt}</span>
            </>
          )}
        </div>
      </div>

      {onMoreClick && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onMoreClick();
          }}
          className="absolute top-2 right-2 p-1 bg-black bg-opacity-50 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
          aria-label="More options"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
          </svg>
        </button>
      )}
    </div>
  );
}
