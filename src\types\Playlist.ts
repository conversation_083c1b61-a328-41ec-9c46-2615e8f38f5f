/**
 * Unified Playlist Type Definition
 * 
 * This is the single source of truth for playlist-related types across the application.
 */

import type { BaseEntity } from './BaseEntity';

export type PlaylistVisibility = 'public' | 'unlisted' | 'private';

export interface PlaylistVideo {
  videoId: string;
  addedAt: string;
  order: number;
  position?: number; // Legacy alias
  note?: string;
}

export interface PlaylistSettings {
  privacy: PlaylistVisibility;
  allowComments: boolean;
  allowRatings: boolean;
  defaultLanguage: string;
}

/**
 * Unified Playlist Interface
 * 
 * This interface consolidates all playlist properties from different features
 * and provides a single source of truth for playlist data.
 */
export interface Playlist extends BaseEntity {
  // Core identification
  title: string;
  description?: string;
  
  // Visual assets
  thumbnailUrl?: string;
  thumbnail?: string; // Legacy alias
  
  // Content
  videos: PlaylistVideo[];
  videoIds?: string[]; // Legacy simplified format
  
  // Statistics
  videoCount: number;
  totalDuration: string;
  totalViews?: number;
  
  // Ownership
  ownerId: string;
  ownerName: string;
  channelId?: string; // Legacy alias
  channelName?: string; // Legacy alias
  
  // Settings and metadata
  visibility: PlaylistVisibility;
  settings?: PlaylistSettings;
  tags: string[];
  
  // Analytics (for strict playlists)
  stats?: {
    views: number;
    likes: number;
    shares: number;
  };
}

// User-specific playlists (legacy support)
export interface UserPlaylist {
  id: string;
  title: string;
  description?: string;
  videoIds: string[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface UserPlaylistDetails extends UserPlaylist {
  videoCount: number;
  thumbnailUrl?: string; // Thumbnail of the first video, or a default
}

// Strict playlist type for new implementations
export interface StrictPlaylist {
  id: string;
  title: string;
  description: string;
  channelId: string;
  videoIds: string[];
  thumbnailUrl: string;
  settings: PlaylistSettings;
  createdAt: string;
  updatedAt: string;
  stats: {
    views: number;
    likes: number;
    shares: number;
  };
}

export default Playlist;
