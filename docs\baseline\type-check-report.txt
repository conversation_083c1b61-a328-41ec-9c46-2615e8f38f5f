src/components/ChannelTabContent.tsx(7,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/components/ChannelTabContent.tsx(33,63): error TS18048: 'b.views' is possibly 'undefined'.
src/components/ChannelTabContent.tsx(33,105): error TS18048: 'a.views' is possibly 'undefined'.
src/components/ConsolidatedVideoCard.tsx(276,41): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/components/Header.tsx(8,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/components/HomeContent.tsx(3,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/components/Layout.tsx(35,23): error TS2339: Property 'actions' does not exist on type 'MiniplayerContextType'.
src/components/Layout.tsx(61,26): error TS2339: Property 'state' does not exist on type 'MiniplayerContextType'.
src/components/Layout.tsx(61,63): error TS2339: Property 'state' does not exist on type 'MiniplayerContextType'.
src/components/Layout.tsx(63,36): error TS2339: Property 'state' does not exist on type 'MiniplayerContextType'.
src/components/Layout.tsx(64,38): error TS2339: Property 'actions' does not exist on type 'MiniplayerContextType'.
src/components/LiveStreams.tsx(6,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/components/OptimizedVideoCard.tsx(17,30): error TS2307: Cannot find module '../src/lib/youtube-utils' or its corresponding type declarations.
src/components/OptimizedVideoCard.tsx(266,56): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/components/RecommendationEngine.tsx(380,55): error TS2769: No overload matches this call.
  Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.
    Argument of type 'string | undefined' is not assignable to parameter of type 'string | number | Date'.
      Type 'undefined' is not assignable to type 'string | number | Date'.
  Overload 2 of 4, '(value: string | number): Date', gave the following error.
    Argument of type 'string | undefined' is not assignable to parameter of type 'string | number'.
      Type 'undefined' is not assignable to type 'string | number'.
src/components/RefactoredSaveToPlaylistModal.tsx(9,31): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/components/ReusableVideoGrid.tsx(3,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/components/SearchBar.tsx(6,23): error TS2307: Cannot find module '../../components/icons/ClockIcon' or its corresponding type declarations.
src/components/SearchBar.tsx(7,24): error TS2307: Cannot find module '../../components/icons/SearchIcon' or its corresponding type declarations.
src/components/SearchBar.tsx(8,31): error TS2307: Cannot find module '../../components/SearchSuggestions' or its corresponding type declarations.
src/components/SearchBar.tsx(15,8): error TS2307: Cannot find module '../../services/mockVideoService' or its corresponding type declarations.
src/components/SearchResults.tsx(5,41): error TS2307: Cannot find module '../../hooks/useIntersectionObserver' or its corresponding type declarations.
src/components/SearchResults.tsx(6,26): error TS2307: Cannot find module '../../utils/componentOptimizations' or its corresponding type declarations.
src/components/SearchResults.tsx(7,36): error TS2307: Cannot find module '../../utils/performance' or its corresponding type declarations.
src/components/SearchResults.tsx(11,62): error TS2307: Cannot find module '../../services/googleSearchService' or its corresponding type declarations.
src/components/SearchResults.tsx(12,28): error TS2307: Cannot find module '../../types' or its corresponding type declarations.
src/components/SearchResults.tsx(365,41): error TS7006: Parameter 'prevProps' implicitly has an 'any' type.
src/components/SearchResults.tsx(365,52): error TS7006: Parameter 'nextProps' implicitly has an 'any' type.
src/components/ShortDisplayCard.tsx(8,32): error TS2307: Cannot find module '../src/hooks/useVideoPlayer' or its corresponding type declarations.
src/components/ShortDisplayCard.tsx(12,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/components/ShortsPlayer.tsx(17,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/components/ShortsPlayer.tsx(305,49): error TS7006: Parameter 'tag' implicitly has an 'any' type.
src/components/ShortsPlayer.tsx(305,54): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/components/ShortsSection.tsx(10,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/components/SubscriptionFeed.tsx(6,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/components/TrendingSection.tsx(6,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/components/unified/UnifiedButton.tsx(9,28): error TS2307: Cannot find module '../../../components/ui/LoadingSpinner' or its corresponding type declarations.
src/components/unified/UnifiedVideoCard.tsx(304,41): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string | Date'.
  Type 'undefined' is not assignable to type 'string | Date'.
src/components/UnifiedVideoCard.tsx(6,31): error TS2307: Cannot find module '../src/components/unified/UnifiedButton' or its corresponding type declarations.
src/components/VideoCard.tsx(116,3): error TS6133: 'size' is declared but its value is never read.
src/components/VideoMetadata.tsx(18,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/components/VideoMetadata.tsx(201,47): error TS7006: Parameter 'tag' implicitly has an 'any' type.
src/components/VideoMetadata.tsx(201,52): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/components/VideoPlaybackDetails.tsx(3,49): error TS2307: Cannot find module '../src/lib/youtube-utils' or its corresponding type declarations.
src/components/VideoPlaybackDetails.tsx(77,13): error TS2322: Type '{ src: string | undefined; poster: string; title: string; }' is not assignable to type 'IntrinsicAttributes & AdvancedVideoPlayerProps'.
  Property 'src' does not exist on type 'IntrinsicAttributes & AdvancedVideoPlayerProps'.
src/components/VideoPlaybackDetails.tsx(91,35): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/components/VideoPlaybackDetails.tsx(111,11): error TS2375: Type 'import("C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/video").Video' is not assignable to type 'Video' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Property 'views' is optional in type 'Video' but required in type 'Video'.
src/components/WatchHistory.tsx(6,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/components/YouTubePlayer.tsx(3,35): error TS2307: Cannot find module '../src/lib/youtube-utils' or its corresponding type declarations.
src/components/YouTubePlayer.tsx(6,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/config/config/routes.tsx(4,27): error TS2307: Cannot find module '../components/AccountLayout' or its corresponding type declarations.
src/config/config/routes.tsx(5,27): error TS2307: Cannot find module '../components/ErrorBoundary' or its corresponding type declarations.
src/config/config/routes.tsx(6,20): error TS2307: Cannot find module '../components/Layout' or its corresponding type declarations.
src/config/config/routes.tsx(7,28): error TS2307: Cannot find module '../components/ProtectedRoute' or its corresponding type declarations.
src/config/config/routes.tsx(8,26): error TS2307: Cannot find module '../components/StudioLayout' or its corresponding type declarations.
src/config/config/routes.tsx(13,36): error TS2307: Cannot find module '../pages/HomePage' or its corresponding type declarations.
src/config/config/routes.tsx(14,37): error TS2307: Cannot find module '../pages/WatchPage' or its corresponding type declarations.
src/config/config/routes.tsx(15,45): error TS2307: Cannot find module '../pages/SearchResultsPage' or its corresponding type declarations.
src/config/config/routes.tsx(16,37): error TS2307: Cannot find module '../pages/LoginPage' or its corresponding type declarations.
src/config/config/routes.tsx(17,40): error TS2307: Cannot find module '../pages/RegisterPage' or its corresponding type declarations.
src/config/config/routes.tsx(18,40): error TS2307: Cannot find module '../pages/TrendingPage' or its corresponding type declarations.
src/config/config/routes.tsx(19,38): error TS2307: Cannot find module '../pages/ShortsPage' or its corresponding type declarations.
src/config/config/routes.tsx(20,45): error TS2307: Cannot find module '../pages/SubscriptionsPage' or its corresponding type declarations.
src/config/config/routes.tsx(21,39): error TS2307: Cannot find module '../pages/HistoryPage' or its corresponding type declarations.
src/config/config/routes.tsx(22,42): error TS2307: Cannot find module '../pages/WatchLaterPage' or its corresponding type declarations.
src/config/config/routes.tsx(23,43): error TS2307: Cannot find module '../pages/LikedVideosPage' or its corresponding type declarations.
src/config/config/routes.tsx(24,39): error TS2307: Cannot find module '../pages/ChannelPage' or its corresponding type declarations.
src/config/config/routes.tsx(25,36): error TS2307: Cannot find module '../pages/UserPage' or its corresponding type declarations.
src/config/config/routes.tsx(26,41): error TS2307: Cannot find module '../pages/PlaylistsPage' or its corresponding type declarations.
src/config/config/routes.tsx(27,46): error TS2307: Cannot find module '../pages/PlaylistDetailPage' or its corresponding type declarations.
src/config/config/routes.tsx(28,39): error TS2307: Cannot find module '../pages/LibraryPage' or its corresponding type declarations.
src/config/config/routes.tsx(29,40): error TS2307: Cannot find module '../pages/YourDataPage' or its corresponding type declarations.
src/config/config/routes.tsx(30,38): error TS2307: Cannot find module '../pages/GoLivePage' or its corresponding type declarations.
src/config/config/routes.tsx(31,46): error TS2307: Cannot find module '../pages/AIContentSparkPage' or its corresponding type declarations.
src/config/config/routes.tsx(32,43): error TS2307: Cannot find module '../pages/VideoUploadPage' or its corresponding type declarations.
src/config/config/routes.tsx(33,40): error TS2307: Cannot find module '../pages/SettingsPage' or its corresponding type declarations.
src/config/config/routes.tsx(34,37): error TS2307: Cannot find module '../pages/AdminPage' or its corresponding type declarations.
src/config/config/routes.tsx(37,38): error TS2307: Cannot find module '../pages/StudioPage' or its corresponding type declarations.
src/config/config/routes.tsx(38,38): error TS2307: Cannot find module '../pages/UploadPage' or its corresponding type declarations.
src/config/config/routes.tsx(39,47): error TS2307: Cannot find module '../pages/StudioDashboardPage' or its corresponding type declarations.
src/config/config/routes.tsx(40,41): error TS2307: Cannot find module '../pages/AnalyticsPage' or its corresponding type declarations.
src/config/config/routes.tsx(41,49): error TS2307: Cannot find module '../pages/CommentModerationPage' or its corresponding type declarations.
src/config/config/routes.tsx(42,44): error TS2307: Cannot find module '../pages/MonetizationPage' or its corresponding type declarations.
src/config/config/routes.tsx(43,46): error TS2307: Cannot find module '../pages/ContentManagerPage' or its corresponding type declarations.
src/config/config/routes.tsx(44,45): error TS2307: Cannot find module '../pages/CreatorStudioPage' or its corresponding type declarations.
src/config/config/routes.tsx(45,41): error TS2307: Cannot find module '../pages/CommunityPage' or its corresponding type declarations.
src/config/config/routes.tsx(46,47): error TS2307: Cannot find module '../pages/PlaylistManagerPage' or its corresponding type declarations.
src/config/config/routes.tsx(47,52): error TS2307: Cannot find module '../pages/ChannelCustomizationPage' or its corresponding type declarations.
src/config/config/routes.tsx(48,43): error TS2307: Cannot find module '../pages/VideoEditorPage' or its corresponding type declarations.
src/config/routes.tsx(31,14): error TS2451: Cannot redeclare block-scoped variable 'routes'.
src/config/routes.tsx(51,19): error TS2304: Cannot find name 'SearchResultsPage'.
src/config/routes.tsx(55,19): error TS2304: Cannot find name 'TrendingPage'.
src/config/routes.tsx(59,19): error TS2304: Cannot find name 'ShortsPage'.
src/config/routes.tsx(63,19): error TS2304: Cannot find name 'SubscriptionsPage'.
src/config/routes.tsx(67,19): error TS2304: Cannot find name 'HistoryPage'.
src/config/routes.tsx(71,19): error TS2304: Cannot find name 'HistoryPage'.
src/config/routes.tsx(75,19): error TS2304: Cannot find name 'PlaylistsPage'.
src/config/routes.tsx(79,19): error TS2304: Cannot find name 'PlaylistDetailPage'.
src/config/routes.tsx(83,19): error TS2304: Cannot find name 'WatchLaterPage'.
src/config/routes.tsx(87,19): error TS2304: Cannot find name 'LikedVideosPage'.
src/config/routes.tsx(91,19): error TS2304: Cannot find name 'ChannelPage'.
src/config/routes.tsx(95,19): error TS2304: Cannot find name 'UserPage'.
src/config/routes.tsx(99,19): error TS2304: Cannot find name 'LibraryPage'.
src/config/routes.tsx(103,19): error TS2304: Cannot find name 'YourDataPage'.
src/config/routes.tsx(107,19): error TS2304: Cannot find name 'GoLivePage'.
src/config/routes.tsx(111,19): error TS2304: Cannot find name 'AIContentSparkPage'.
src/config/routes.tsx(115,19): error TS2304: Cannot find name 'VideoUploadPage'.
src/config/routes.tsx(119,19): error TS2304: Cannot find name 'SettingsPage'.
src/config/routes.tsx(123,19): error TS2304: Cannot find name 'AdminPage'.
src/config/routes.tsx(127,19): error TS2304: Cannot find name 'StudioPage'.
src/config/routes.tsx(131,19): error TS2304: Cannot find name 'UploadPage'.
src/config/routes.tsx(136,19): error TS2304: Cannot find name 'AccountLayout'.
src/config/routes.tsx(140,23): error TS2304: Cannot find name 'SettingsPage'.
src/config/routes.tsx(144,23): error TS2304: Cannot find name 'SettingsPage'.
src/config/routes.tsx(148,23): error TS2304: Cannot find name 'YourDataPage'.
src/config/routes.tsx(154,19): error TS2304: Cannot find name 'AnalyticsPage'.
src/config/routes.tsx(159,19): error TS2304: Cannot find name 'ContentManagerPage'.
src/config/routes.tsx(169,15): error TS2304: Cannot find name 'StudioLayout'.
src/config/routes.tsx(174,19): error TS2304: Cannot find name 'StudioDashboardPage'.
src/config/routes.tsx(178,19): error TS2304: Cannot find name 'AnalyticsPage'.
src/config/routes.tsx(182,19): error TS2304: Cannot find name 'CommentModerationPage'.
src/config/routes.tsx(186,19): error TS2304: Cannot find name 'MonetizationPage'.
src/config/routes.tsx(190,19): error TS2304: Cannot find name 'ContentManagerPage'.
src/config/routes.tsx(194,19): error TS2304: Cannot find name 'CreatorStudioPage'.
src/config/routes.tsx(198,19): error TS2304: Cannot find name 'CommunityPage'.
src/config/routes.tsx(202,19): error TS2304: Cannot find name 'PlaylistManagerPage'.
src/config/routes.tsx(206,19): error TS2304: Cannot find name 'ChannelCustomizationPage'.
src/config/routes.tsx(210,19): error TS2304: Cannot find name 'VideoEditorPage'.
src/config/routes.tsx(221,8): error TS2304: Cannot find name 'ProtectedRoute'.
src/config/routes.tsx(222,10): error TS2304: Cannot find name 'LoginPage'.
src/config/routes.tsx(223,9): error TS2304: Cannot find name 'ProtectedRoute'.
src/config/routes.tsx(230,8): error TS2304: Cannot find name 'ProtectedRoute'.
src/config/routes.tsx(231,10): error TS2304: Cannot find name 'RegisterPage'.
src/config/routes.tsx(232,9): error TS2304: Cannot find name 'ProtectedRoute'.
src/config/routes.tsx(239,14): error TS2451: Cannot redeclare block-scoped variable 'routes'.
src/config/routes.tsx(239,42): error TS2304: Cannot find name 'mainRoutes'.
src/contexts/AuthContext.tsx(1,17): error TS2300: Duplicate identifier 'createContext'.
src/contexts/AuthContext.tsx(1,32): error TS2300: Duplicate identifier 'useContext'.
src/contexts/AuthContext.tsx(1,44): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/AuthContext.tsx(9,7): error TS2451: Cannot redeclare block-scoped variable 'AuthContext'.
src/contexts/AuthContext.tsx(11,14): error TS2451: Cannot redeclare block-scoped variable 'AuthProvider'.
src/contexts/AuthContext.tsx(23,27): error TS2739: Type '{ user: null; login: () => void; logout: () => void; }' is missing the following properties from type 'AuthContextType': isAuthenticated, isLoading, register, updateProfile
src/contexts/AuthContext.tsx(29,14): error TS2451: Cannot redeclare block-scoped variable 'useAuth'.
src/contexts/AuthContext.tsx(38,10): error TS2300: Duplicate identifier 'createContext'.
src/contexts/AuthContext.tsx(38,25): error TS2300: Duplicate identifier 'useContext'.
src/contexts/AuthContext.tsx(38,63): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/AuthContext.tsx(51,3): error TS2717: Subsequent property declarations must have the same type.  Property 'user' must be of type 'any', but here has type 'User | null'.
src/contexts/AuthContext.tsx(54,3): error TS2717: Subsequent property declarations must have the same type.  Property 'login' must be of type '(username: string, password: string) => void', but here has type '(email: string, password: string) => Promise<boolean>'.
src/contexts/AuthContext.tsx(60,7): error TS2451: Cannot redeclare block-scoped variable 'AuthContext'.
src/contexts/AuthContext.tsx(62,14): error TS2451: Cannot redeclare block-scoped variable 'useAuth'.
src/contexts/AuthContext.tsx(74,14): error TS2451: Cannot redeclare block-scoped variable 'AuthProvider'.
src/contexts/MiniplayerContext.tsx(4,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/contexts/OptimizedMiniplayerContext.tsx(1,17): error TS2300: Duplicate identifier 'createContext'.
src/contexts/OptimizedMiniplayerContext.tsx(1,32): error TS2300: Duplicate identifier 'useContext'.
src/contexts/OptimizedMiniplayerContext.tsx(1,44): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/OptimizedMiniplayerContext.tsx(9,7): error TS2451: Cannot redeclare block-scoped variable 'MiniplayerContext'.
src/contexts/OptimizedMiniplayerContext.tsx(11,14): error TS2451: Cannot redeclare block-scoped variable 'OptimizedMiniplayerProvider'.
src/contexts/OptimizedMiniplayerContext.tsx(29,14): error TS2451: Cannot redeclare block-scoped variable 'useMiniplayerActions'.
src/contexts/OptimizedMiniplayerContext.tsx(37,10): error TS2300: Duplicate identifier 'createContext'.
src/contexts/OptimizedMiniplayerContext.tsx(37,25): error TS2300: Duplicate identifier 'useContext'.
src/contexts/OptimizedMiniplayerContext.tsx(37,63): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/OptimizedMiniplayerContext.tsx(235,7): error TS2451: Cannot redeclare block-scoped variable 'MiniplayerContext'.
src/contexts/OptimizedMiniplayerContext.tsx(242,14): error TS2451: Cannot redeclare block-scoped variable 'OptimizedMiniplayerProvider'.
src/contexts/OptimizedMiniplayerContext.tsx(267,33): error TS2739: Type '{ state: MiniplayerState; actions: { showMiniplayer: (video: Video) => void; hideMiniplayer: () => void; togglePlay: () => void; setPlaying: (playing: boolean) => void; ... 10 more ...; setCurrentIndex: (index: number) => void; }; }' is missing the following properties from type 'MiniplayerContextType': isVisible, showMiniplayer, hideMiniplayer
src/contexts/OptimizedMiniplayerContext.tsx(284,11): error TS2339: Property 'state' does not exist on type 'MiniplayerContextType'.
src/contexts/OptimizedMiniplayerContext.tsx(289,11): error TS2339: Property 'state' does not exist on type 'MiniplayerContextType'.
src/contexts/OptimizedMiniplayerContext.tsx(294,11): error TS2339: Property 'state' does not exist on type 'MiniplayerContextType'.
src/contexts/OptimizedMiniplayerContext.tsx(304,11): error TS2339: Property 'state' does not exist on type 'MiniplayerContextType'.
src/contexts/OptimizedMiniplayerContext.tsx(311,14): error TS2451: Cannot redeclare block-scoped variable 'useMiniplayerActions'.
src/contexts/OptimizedMiniplayerContext.tsx(312,11): error TS2339: Property 'actions' does not exist on type 'MiniplayerContextType'.
src/contexts/ThemeContext.tsx(1,17): error TS2300: Duplicate identifier 'createContext'.
src/contexts/ThemeContext.tsx(1,32): error TS2300: Duplicate identifier 'useContext'.
src/contexts/ThemeContext.tsx(1,44): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/ThemeContext.tsx(8,7): error TS2451: Cannot redeclare block-scoped variable 'ThemeContext'.
src/contexts/ThemeContext.tsx(10,14): error TS2451: Cannot redeclare block-scoped variable 'ThemeProvider'.
src/contexts/ThemeContext.tsx(24,14): error TS2451: Cannot redeclare block-scoped variable 'useTheme'.
src/contexts/ThemeContext.tsx(33,10): error TS2300: Duplicate identifier 'createContext'.
src/contexts/ThemeContext.tsx(33,35): error TS2300: Duplicate identifier 'useContext'.
src/contexts/ThemeContext.tsx(33,63): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/ThemeContext.tsx(38,3): error TS2717: Subsequent property declarations must have the same type.  Property 'theme' must be of type 'string', but here has type 'Theme'.
src/contexts/ThemeContext.tsx(42,7): error TS2451: Cannot redeclare block-scoped variable 'ThemeContext'.
src/contexts/ThemeContext.tsx(44,14): error TS2451: Cannot redeclare block-scoped variable 'ThemeProvider'.
src/contexts/ThemeContext.tsx(81,14): error TS2451: Cannot redeclare block-scoped variable 'useTheme'.
src/contexts/UnifiedAppContext.tsx(1,8): error TS2300: Duplicate identifier 'React'.
src/contexts/UnifiedAppContext.tsx(1,17): error TS2300: Duplicate identifier 'createContext'.
src/contexts/UnifiedAppContext.tsx(1,32): error TS2300: Duplicate identifier 'useContext'.
src/contexts/UnifiedAppContext.tsx(1,44): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/UnifiedAppContext.tsx(7,7): error TS2451: Cannot redeclare block-scoped variable 'UnifiedAppContext'.
src/contexts/UnifiedAppContext.tsx(9,14): error TS2451: Cannot redeclare block-scoped variable 'UnifiedAppProvider'.
src/contexts/UnifiedAppContext.tsx(13,33): error TS2740: Type '{ appState: {}; }' is missing the following properties from type 'UnifiedAppContextType': state, login, logout, updateProfile, and 10 more.
src/contexts/UnifiedAppContext.tsx(19,14): error TS2451: Cannot redeclare block-scoped variable 'useUnifiedApp'.
src/contexts/UnifiedAppContext.tsx(27,8): error TS2300: Duplicate identifier 'React'.
src/contexts/UnifiedAppContext.tsx(27,17): error TS2300: Duplicate identifier 'createContext'.
src/contexts/UnifiedAppContext.tsx(27,32): error TS2300: Duplicate identifier 'useContext'.
src/contexts/UnifiedAppContext.tsx(27,74): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/UnifiedAppContext.tsx(29,27): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/contexts/UnifiedAppContext.tsx(169,7): error TS2451: Cannot redeclare block-scoped variable 'UnifiedAppContext'.
src/contexts/UnifiedAppContext.tsx(172,14): error TS2451: Cannot redeclare block-scoped variable 'useUnifiedApp'.
src/contexts/UnifiedAppContext.tsx(186,14): error TS2451: Cannot redeclare block-scoped variable 'UnifiedAppProvider'.
src/contexts/UnifiedAppContext.tsx(357,9): error TS2741: Property 'appState' is missing in type '{ state: UnifiedAppState; login: (email: string, password: string) => Promise<boolean>; logout: () => void; updateProfile: (updates: Partial<User>) => Promise<boolean>; ... 9 more ...; removeNotification: (id: string) => void; }' but required in type 'UnifiedAppContextType'.
src/contexts/WatchLaterContext.tsx(1,17): error TS2300: Duplicate identifier 'createContext'.
src/contexts/WatchLaterContext.tsx(1,32): error TS2300: Duplicate identifier 'useContext'.
src/contexts/WatchLaterContext.tsx(1,44): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/WatchLaterContext.tsx(9,7): error TS2451: Cannot redeclare block-scoped variable 'WatchLaterContext'.
src/contexts/WatchLaterContext.tsx(11,14): error TS2451: Cannot redeclare block-scoped variable 'WatchLaterProvider'.
src/contexts/WatchLaterContext.tsx(23,33): error TS2739: Type '{ videos: any[]; addToWatchLater: () => void; removeFromWatchLater: () => void; }' is missing the following properties from type 'WatchLaterContextType': watchLaterList, isWatchLater
src/contexts/WatchLaterContext.tsx(29,14): error TS2451: Cannot redeclare block-scoped variable 'useWatchLater'.
src/contexts/WatchLaterContext.tsx(37,10): error TS2300: Duplicate identifier 'createContext'.
src/contexts/WatchLaterContext.tsx(37,25): error TS2300: Duplicate identifier 'useContext'.
src/contexts/WatchLaterContext.tsx(37,63): error TS2300: Duplicate identifier 'ReactNode'.
src/contexts/WatchLaterContext.tsx(39,41): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/contexts/WatchLaterContext.tsx(53,7): error TS2451: Cannot redeclare block-scoped variable 'WatchLaterContext'.
src/contexts/WatchLaterContext.tsx(55,14): error TS2451: Cannot redeclare block-scoped variable 'WatchLaterProvider'.
src/contexts/WatchLaterContext.tsx(83,33): error TS2741: Property 'videos' is missing in type '{ watchLaterList: VideoType[]; addToWatchLater: (video: VideoType) => void; removeFromWatchLater: (videoId: string) => void; isWatchLater: (videoId: string) => boolean; }' but required in type 'WatchLaterContextType'.
src/contexts/WatchLaterContext.tsx(89,14): error TS2451: Cannot redeclare block-scoped variable 'useWatchLater'.
src/features/analytics/pages/AnalyticsPage.tsx(5,27): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/analytics/pages/AnalyticsPage.tsx(6,32): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/analytics/pages/AnalyticsPage.tsx(8,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/analytics/pages/AnalyticsPage.tsx(49,38): error TS7006: Parameter 'sum' implicitly has an 'any' type.
src/features/analytics/pages/AnalyticsPage.tsx(49,43): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/analytics/pages/AnalyticsPage.tsx(66,53): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/auth/components/ProtectedRoute.tsx(5,28): error TS2307: Cannot find module '../../../../components/ui/LoadingSpinner' or its corresponding type declarations.
src/features/auth/pages/LoginPage.tsx(6,20): error TS2307: Cannot find module '../components/forms/Button' or its corresponding type declarations.
src/features/auth/pages/LoginPage.tsx(7,19): error TS2307: Cannot find module '../components/forms/Input' or its corresponding type declarations.
src/features/auth/pages/LoginPage.tsx(8,25): error TS2307: Cannot find module '../components/icons/YouTubeLogo' or its corresponding type declarations.
src/features/auth/pages/LoginPage.tsx(9,25): error TS2307: Cannot find module '../contexts/AuthContext' or its corresponding type declarations.
src/features/auth/pages/LoginPage.tsx(102,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/auth/pages/LoginPage.tsx(121,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/auth/pages/RegisterPage.tsx(6,20): error TS2307: Cannot find module '../components/forms/Button' or its corresponding type declarations.
src/features/auth/pages/RegisterPage.tsx(7,19): error TS2307: Cannot find module '../components/forms/Input' or its corresponding type declarations.
src/features/auth/pages/RegisterPage.tsx(8,25): error TS2307: Cannot find module '../components/icons/YouTubeLogo' or its corresponding type declarations.
src/features/auth/pages/RegisterPage.tsx(9,25): error TS2307: Cannot find module '../contexts/AuthContext' or its corresponding type declarations.
src/features/auth/pages/RegisterPage.tsx(126,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/auth/pages/RegisterPage.tsx(148,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/auth/pages/RegisterPage.tsx(167,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/auth/pages/RegisterPage.tsx(189,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/channel/pages/ChannelPage.tsx(8,27): error TS2307: Cannot find module '../components/ChannelHeader' or its corresponding type declarations.
src/features/channel/pages/ChannelPage.tsx(9,31): error TS2307: Cannot find module '../components/ChannelTabContent' or its corresponding type declarations.
src/features/channel/pages/ChannelPage.tsx(10,25): error TS2307: Cannot find module '../components/ChannelTabs' or its corresponding type declarations.
src/features/channel/pages/ChannelPage.tsx(11,33): error TS2307: Cannot find module '../components/LoadingStates/ChannelPageSkeleton' or its corresponding type declarations.
src/features/channel/pages/ChannelPage.tsx(12,105): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/channel/pages/ChannelPage.tsx(14,69): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/channel/pages/UserPage.tsx(8,27): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/channel/pages/UserPage.tsx(9,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/channel/pages/UserPage.tsx(11,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/channel/pages/UserPage.tsx(30,49): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/creator/pages/AIContentSparkPage.tsx(7,36): error TS2307: Cannot find module '../services/geminiService' or its corresponding type declarations.
src/features/creator/pages/AIContentSparkPage.tsx(9,40): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/creator/pages/CreatorStudioPage.tsx(19,37): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/creator/pages/CreatorStudioPage.tsx(20,30): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/creator/pages/UploadPage.tsx(15,58): error TS2307: Cannot find module '../components/ui/Tabs' or its corresponding type declarations.
src/features/creator/pages/UploadPage.tsx(16,31): error TS2307: Cannot find module '../components/ui/UnifiedButton' or its corresponding type declarations.
src/features/creator/pages/VideoUploadPage.tsx(6,29): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/creator/pages/VideoUploadPage.tsx(8,54): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/creator/pages/VideoUploadPage.tsx(59,19): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(92,21): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(101,19): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(103,30): error TS7006: Parameter 'tag' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(117,38): error TS7006: Parameter 'progressData' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(200,52): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(236,33): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(254,48): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(276,50): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(299,35): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(314,50): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(330,50): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(370,41): error TS7006: Parameter 'tag' implicitly has an 'any' type.
src/features/creator/pages/VideoUploadPage.tsx(370,46): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/features/misc/pages/AdminPage.tsx(21,8): error TS2307: Cannot find module '../services/settingsService' or its corresponding type declarations.
src/features/misc/pages/AdminPage.tsx(176,35): error TS7006: Parameter 'feature' implicitly has an 'any' type.
src/features/misc/pages/AdminPage.tsx(176,44): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/features/misc/pages/AdminPage.tsx(187,47): error TS7006: Parameter 'useCase' implicitly has an 'any' type.
src/features/misc/pages/AdminPage.tsx(187,56): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/features/misc/pages/AdminPage.tsx(370,62): error TS7006: Parameter 'config' implicitly has an 'any' type.
src/features/misc/pages/AdminPage.tsx(397,60): error TS7006: Parameter 'config' implicitly has an 'any' type.
src/features/misc/pages/AIContentSparkPage.tsx(7,36): error TS2307: Cannot find module '../services/geminiService' or its corresponding type declarations.
src/features/misc/pages/AIContentSparkPage.tsx(9,40): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/AnalyticsPage.tsx(5,27): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/AnalyticsPage.tsx(6,32): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/misc/pages/AnalyticsPage.tsx(8,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/AnalyticsPage.tsx(49,38): error TS7006: Parameter 'sum' implicitly has an 'any' type.
src/features/misc/pages/AnalyticsPage.tsx(49,43): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/AnalyticsPage.tsx(66,53): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/ChannelPage.tsx(8,27): error TS2307: Cannot find module '../components/ChannelHeader' or its corresponding type declarations.
src/features/misc/pages/ChannelPage.tsx(9,31): error TS2307: Cannot find module '../components/ChannelTabContent' or its corresponding type declarations.
src/features/misc/pages/ChannelPage.tsx(10,25): error TS2307: Cannot find module '../components/ChannelTabs' or its corresponding type declarations.
src/features/misc/pages/ChannelPage.tsx(11,33): error TS2307: Cannot find module '../components/LoadingStates/ChannelPageSkeleton' or its corresponding type declarations.
src/features/misc/pages/ChannelPage.tsx(12,105): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/ChannelPage.tsx(14,69): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/CommentModerationPage.tsx(6,49): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/CommentModerationPage.tsx(7,35): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/misc/pages/CommentModerationPage.tsx(9,30): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/CommentModerationPage.tsx(41,55): error TS7006: Parameter 'comment' implicitly has an 'any' type.
src/features/misc/pages/CommentModerationPage.tsx(77,17): error TS2339: Property 'commentText' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(78,17): error TS2339: Property 'userName' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(87,29): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(87,63): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(89,29): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(89,63): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(91,20): error TS2339: Property 'likes' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(91,30): error TS2339: Property 'likes' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(93,21): error TS2339: Property 'replyCount' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(93,43): error TS2339: Property 'replyCount' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(118,63): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(126,42): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(145,21): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(339,33): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(343,59): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(344,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(348,34): error TS2339: Property 'userAvatarUrl' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(349,34): error TS2339: Property 'userName' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(355,34): error TS2339: Property 'userName' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(359,52): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(363,32): error TS2339: Property 'commentText' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(367,38): error TS2339: Property 'likes' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(368,32): error TS2339: Property 'replyCount' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(368,61): error TS2339: Property 'replyCount' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(381,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(388,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(395,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/CommentModerationPage.tsx(402,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/misc/pages/ContentManagerPage.tsx(6,27): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/ContentManagerPage.tsx(7,35): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/misc/pages/ContentManagerPage.tsx(8,30): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/misc/pages/ContentManagerPage.tsx(10,34): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/ContentManagerPage.tsx(36,56): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/CreatorStudioPage.tsx(19,37): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/misc/pages/CreatorStudioPage.tsx(20,30): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/misc/pages/HistoryPage.tsx(5,25): error TS2307: Cannot find module '../components/icons/HistoryIcon' or its corresponding type declarations.
src/features/misc/pages/HistoryPage.tsx(6,33): error TS2307: Cannot find module '../components/LoadingStates/HistoryPageSkeleton' or its corresponding type declarations.
src/features/misc/pages/HistoryPage.tsx(7,39): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/HistoryPage.tsx(8,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/misc/pages/HistoryPage.tsx(10,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/HomePage.tsx(7,27): error TS2307: Cannot find module '../components/CategoryChips' or its corresponding type declarations.
src/features/misc/pages/HomePage.tsx(8,25): error TS2307: Cannot find module '../components/HomeContent' or its corresponding type declarations.
src/features/misc/pages/HomePage.tsx(9,24): error TS2307: Cannot find module '../components/PageLayout' or its corresponding type declarations.
src/features/misc/pages/HomePage.tsx(10,27): error TS2307: Cannot find module '../hooks' or its corresponding type declarations.
src/features/misc/pages/HomePage.tsx(25,26): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/HomePage.tsx(53,9): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/misc/pages/LibraryPage.tsx(9,30): error TS2307: Cannot find module '../components/icons/HistoryIcon' or its corresponding type declarations.
src/features/misc/pages/LibraryPage.tsx(10,32): error TS2307: Cannot find module '../components/icons/PlaylistIcon' or its corresponding type declarations.
src/features/misc/pages/LibraryPage.tsx(16,8): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/LibraryPage.tsx(17,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/misc/pages/LibraryPage.tsx(19,49): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/LibraryPage.tsx(109,38): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/misc/pages/LibraryPage.tsx(112,36): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/misc/pages/LibraryPage.tsx(115,33): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/misc/pages/LibraryPage.tsx(116,46): error TS7006: Parameter 'a' implicitly has an 'any' type.
src/features/misc/pages/LibraryPage.tsx(116,49): error TS7006: Parameter 'b' implicitly has an 'any' type.
src/features/misc/pages/LibraryPage.tsx(121,31): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/misc/pages/LikedVideosPage.tsx(7,37): error TS2307: Cannot find module '../components/LoadingStates/LikedVideosPageSkeleton' or its corresponding type declarations.
src/features/misc/pages/LikedVideosPage.tsx(8,32): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/LikedVideosPage.tsx(9,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/misc/pages/LikedVideosPage.tsx(11,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/LoginPage.tsx(6,20): error TS2307: Cannot find module '../components/forms/Button' or its corresponding type declarations.
src/features/misc/pages/LoginPage.tsx(7,19): error TS2307: Cannot find module '../components/forms/Input' or its corresponding type declarations.
src/features/misc/pages/LoginPage.tsx(8,25): error TS2307: Cannot find module '../components/icons/YouTubeLogo' or its corresponding type declarations.
src/features/misc/pages/LoginPage.tsx(9,25): error TS2307: Cannot find module '../contexts/AuthContext' or its corresponding type declarations.
src/features/misc/pages/LoginPage.tsx(102,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/misc/pages/LoginPage.tsx(121,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/misc/pages/OptimizedHomePage.tsx(6,27): error TS2307: Cannot find module '../components/CategoryChips' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(7,27): error TS2307: Cannot find module '../components/ErrorBoundary' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(8,28): error TS2307: Cannot find module '../components/LoadingSpinner' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(9,34): error TS2307: Cannot find module '../components/VirtualizedVideoGrid' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(10,31): error TS2307: Cannot find module '../hooks/useOptimizedVideoData' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(11,20): error TS2307: Cannot find module '../utils/cn' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(13,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(16,47): error TS2307: Cannot find module '../components/ShortsSection' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(17,49): error TS2307: Cannot find module '../components/TrendingSection' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(18,50): error TS2307: Cannot find module '../components/SubscriptionFeed' or its corresponding type declarations.
src/features/misc/pages/OptimizedHomePage.tsx(63,26): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/PlaylistDetailPage.tsx(8,36): error TS2307: Cannot find module '../components/LoadingStates/PlaylistDetailSkeleton' or its corresponding type declarations.
src/features/misc/pages/PlaylistDetailPage.tsx(9,31): error TS2307: Cannot find module '../components/PlaylistEditModal' or its corresponding type declarations.
src/features/misc/pages/PlaylistDetailPage.tsx(10,89): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/PlaylistDetailPage.tsx(12,42): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/PlaylistDetailPage.tsx(96,106): error TS2339: Property 'title' does not exist on type 'PlaylistWithVideos'.
src/features/misc/pages/PlaylistDetailPage.tsx(111,33): error TS2551: Property 'videoIds' does not exist on type 'PlaylistWithVideos'. Did you mean 'videos'?
src/features/misc/pages/PlaylistDetailPage.tsx(111,49): error TS7006: Parameter 'id' implicitly has an 'any' type.
src/features/misc/pages/PlaylistDetailPage.tsx(124,49): error TS2339: Property 'title' does not exist on type 'PlaylistWithVideos'.
src/features/misc/pages/PlaylistDetailPage.tsx(125,55): error TS2339: Property 'description' does not exist on type 'PlaylistWithVideos'.
src/features/misc/pages/PlaylistDetailPage.tsx(165,11): error TS2339: Property 'title' does not exist on type 'PlaylistWithVideos'.
src/features/misc/pages/PlaylistDetailPage.tsx(165,18): error TS2339: Property 'description' does not exist on type 'PlaylistWithVideos'.
src/features/misc/pages/PlaylistDetailPage.tsx(165,39): error TS2339: Property 'updatedAt' does not exist on type 'PlaylistWithVideos'.
src/features/misc/pages/PlaylistDetailPage.tsx(166,38): error TS2551: Property 'videoIds' does not exist on type 'PlaylistWithVideos'. Did you mean 'videos'?
src/features/misc/pages/PlaylistsPage.tsx(8,54): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/PlaylistsPage.tsx(10,42): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/RefactoredContentManagerPage.tsx(4,22): error TS2307: Cannot find module '../components/BaseForm' or its corresponding type declarations.
src/features/misc/pages/RefactoredContentManagerPage.tsx(5,23): error TS2307: Cannot find module '../components/BaseModal' or its corresponding type declarations.
src/features/misc/pages/RefactoredContentManagerPage.tsx(6,31): error TS2307: Cannot find module '../components/ReusableVideoGrid' or its corresponding type declarations.
src/features/misc/pages/RefactoredContentManagerPage.tsx(7,32): error TS2307: Cannot find module '../components/StandardPageLayout' or its corresponding type declarations.
src/features/misc/pages/RefactoredContentManagerPage.tsx(8,24): error TS2307: Cannot find module '../components/ui/Button' or its corresponding type declarations.
src/features/misc/pages/RefactoredContentManagerPage.tsx(9,58): error TS2307: Cannot find module '../components/ui/Tabs' or its corresponding type declarations.
src/features/misc/pages/RefactoredContentManagerPage.tsx(11,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/RefactoredTrendingPage.tsx(3,31): error TS2307: Cannot find module '../components/ReusableVideoGrid' or its corresponding type declarations.
src/features/misc/pages/RefactoredTrendingPage.tsx(4,32): error TS2307: Cannot find module '../components/StandardPageLayout' or its corresponding type declarations.
src/features/misc/pages/RefactoredTrendingPage.tsx(5,31): error TS2307: Cannot find module '../hooks' or its corresponding type declarations.
src/features/misc/pages/RefactoredTrendingPage.tsx(7,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/RegisterPage.tsx(6,20): error TS2307: Cannot find module '../components/forms/Button' or its corresponding type declarations.
src/features/misc/pages/RegisterPage.tsx(7,19): error TS2307: Cannot find module '../components/forms/Input' or its corresponding type declarations.
src/features/misc/pages/RegisterPage.tsx(8,25): error TS2307: Cannot find module '../components/icons/YouTubeLogo' or its corresponding type declarations.
src/features/misc/pages/RegisterPage.tsx(9,25): error TS2307: Cannot find module '../contexts/AuthContext' or its corresponding type declarations.
src/features/misc/pages/RegisterPage.tsx(126,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/misc/pages/RegisterPage.tsx(148,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/misc/pages/RegisterPage.tsx(167,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/misc/pages/RegisterPage.tsx(189,30): error TS7006: Parameter 'e' implicitly has an 'any' type.
src/features/misc/pages/SearchResultsPage.tsx(8,29): error TS2307: Cannot find module '../hooks/useDebounce' or its corresponding type declarations.
src/features/misc/pages/SearchResultsPage.tsx(9,30): error TS2307: Cannot find module '../services/api' or its corresponding type declarations.
src/features/misc/pages/SearchResultsPage.tsx(10,83): error TS2307: Cannot find module '../services/googleSearchService' or its corresponding type declarations.
src/features/misc/pages/SearchResultsPage.tsx(11,31): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/misc/pages/SearchResultsPage.tsx(12,36): error TS2307: Cannot find module '../utils/performance' or its corresponding type declarations.
src/features/misc/pages/SearchResultsPage.tsx(14,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/SearchResultsPage.tsx(81,10): error TS7006: Parameter 'query' implicitly has an 'any' type.
src/features/misc/pages/SearchResultsPage.tsx(81,58): error TS7006: Parameter 'result' implicitly has an 'any' type.
src/features/misc/pages/SearchResultsPage.tsx(145,24): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/SettingsPage.tsx(8,26): error TS2307: Cannot find module '../contexts/ThemeContext' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(11,26): error TS2307: Cannot find module '../components/CommentModal' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(12,30): error TS2307: Cannot find module '../components/ErrorStates/EmptyShortsState' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(13,29): error TS2307: Cannot find module '../components/ErrorStates/ShortsPageError' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(14,32): error TS2307: Cannot find module '../components/LoadingStates/ShortsPageSkeleton' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(15,30): error TS2307: Cannot find module '../components/ShortDisplayCard' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(16,27): error TS2307: Cannot find module '../components/ShortsFilters' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(17,30): error TS2307: Cannot find module '../components/ShortsNavigation' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(18,63): error TS2307: Cannot find module '../hooks' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(20,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/features/misc/pages/ShortsPage.tsx(84,15): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/ShortsPage.tsx(85,12): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/ShortsPage.tsx(123,56): error TS7006: Parameter 'short' implicitly has an 'any' type.
src/features/misc/pages/ShortsPage.tsx(129,25): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/ShortsPage.tsx(140,30): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/StudioDashboardPage.tsx(18,37): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/misc/pages/StudioDashboardPage.tsx(19,46): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/misc/pages/StudioPage.tsx(21,58): error TS2307: Cannot find module '../components/ui/Tabs' or its corresponding type declarations.
src/features/misc/pages/StudioPage.tsx(22,31): error TS2307: Cannot find module '../components/ui/UnifiedButton' or its corresponding type declarations.
src/features/misc/pages/SubscriptionsPage.tsx(14,31): error TS2307: Cannot find module '../components/icons/SubscriptionsIcon' or its corresponding type declarations.
src/features/misc/pages/SubscriptionsPage.tsx(15,31): error TS2307: Cannot find module '../components/SubscriptionStats' or its corresponding type declarations.
src/features/misc/pages/SubscriptionsPage.tsx(16,35): error TS2307: Cannot find module '../components/SubscriptionVideoCard' or its corresponding type declarations.
src/features/misc/pages/SubscriptionsPage.tsx(17,24): error TS2307: Cannot find module '../components/ui/Button' or its corresponding type declarations.
src/features/misc/pages/SubscriptionsPage.tsx(18,28): error TS2307: Cannot find module '../components/ui/LoadingSpinner' or its corresponding type declarations.
src/features/misc/pages/SubscriptionsPage.tsx(19,58): error TS2307: Cannot find module '../components/ui/Tabs' or its corresponding type declarations.
src/features/misc/pages/SubscriptionsPage.tsx(20,56): error TS2307: Cannot find module '../hooks' or its corresponding type declarations.
src/features/misc/pages/SubscriptionsPage.tsx(101,60): error TS7006: Parameter 'c' implicitly has an 'any' type.
src/features/misc/pages/SubscriptionsPage.tsx(107,53): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/SubscriptionsPage.tsx(219,39): error TS7006: Parameter 'channel' implicitly has an 'any' type.
src/features/misc/pages/SubscriptionsPage.tsx(279,49): error TS7006: Parameter 'value' implicitly has an 'any' type.
src/features/misc/pages/TrendingPage.tsx(7,26): error TS2307: Cannot find module '../components/CategoryTabs' or its corresponding type declarations.
src/features/misc/pages/TrendingPage.tsx(8,24): error TS2307: Cannot find module '../components/PageLayout' or its corresponding type declarations.
src/features/misc/pages/TrendingPage.tsx(9,35): error TS2307: Cannot find module '../hooks' or its corresponding type declarations.
src/features/misc/pages/TrendingPage.tsx(10,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/misc/pages/TrendingPage.tsx(37,9): error TS7006: Parameter 'videos' implicitly has an 'any' type.
src/features/misc/pages/TrendingPage.tsx(39,31): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/UploadPage.tsx(15,58): error TS2307: Cannot find module '../components/ui/Tabs' or its corresponding type declarations.
src/features/misc/pages/UploadPage.tsx(16,31): error TS2307: Cannot find module '../components/ui/UnifiedButton' or its corresponding type declarations.
src/features/misc/pages/UserPage.tsx(8,27): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/UserPage.tsx(9,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/misc/pages/UserPage.tsx(11,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/UserPage.tsx(30,49): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(6,29): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/misc/pages/VideoUploadPage.tsx(8,54): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/VideoUploadPage.tsx(59,19): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(92,21): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(101,19): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(103,30): error TS7006: Parameter 'tag' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(117,38): error TS7006: Parameter 'progressData' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(200,52): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(236,33): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(254,48): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(276,50): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(299,35): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(314,50): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(330,50): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(370,41): error TS7006: Parameter 'tag' implicitly has an 'any' type.
src/features/misc/pages/VideoUploadPage.tsx(370,46): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/features/misc/pages/WatchLaterPage.tsx(6,31): error TS2307: Cannot find module '../contexts/WatchLaterContext' or its corresponding type declarations.
src/features/misc/pages/WatchLaterPage.tsx(7,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/misc/pages/WatchLaterPage.tsx(9,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(4,176): error TS2307: Cannot find module '../components' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(5,27): error TS2307: Cannot find module '../components/VideoMetadata' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(6,38): error TS2307: Cannot find module '../contexts/OptimizedMiniplayerContext' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(7,31): error TS2307: Cannot find module '../contexts/WatchLaterContext' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(8,30): error TS2307: Cannot find module '../hooks/useWatchPage' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(9,38): error TS2307: Cannot find module '../services/settingsService' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(10,49): error TS2307: Cannot find module '../src/lib/youtube-utils' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(11,37): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(12,29): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/misc/pages/WatchPage.tsx(316,31): error TS7006: Parameter 'videoId' implicitly has an 'any' type.
src/features/misc/pages/YourDataPage.tsx(7,98): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/moderation/pages/AdminPage.tsx(21,8): error TS2307: Cannot find module '../services/settingsService' or its corresponding type declarations.
src/features/moderation/pages/AdminPage.tsx(176,35): error TS7006: Parameter 'feature' implicitly has an 'any' type.
src/features/moderation/pages/AdminPage.tsx(176,44): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/features/moderation/pages/AdminPage.tsx(187,47): error TS7006: Parameter 'useCase' implicitly has an 'any' type.
src/features/moderation/pages/AdminPage.tsx(187,56): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/features/moderation/pages/AdminPage.tsx(370,62): error TS7006: Parameter 'config' implicitly has an 'any' type.
src/features/moderation/pages/AdminPage.tsx(397,60): error TS7006: Parameter 'config' implicitly has an 'any' type.
src/features/moderation/pages/CommentModerationPage.tsx(6,49): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/moderation/pages/CommentModerationPage.tsx(7,35): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/moderation/pages/CommentModerationPage.tsx(9,30): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/moderation/pages/CommentModerationPage.tsx(41,55): error TS7006: Parameter 'comment' implicitly has an 'any' type.
src/features/moderation/pages/CommentModerationPage.tsx(77,17): error TS2339: Property 'commentText' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(78,17): error TS2339: Property 'userName' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(87,29): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(87,63): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(89,29): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(89,63): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(91,20): error TS2339: Property 'likes' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(91,30): error TS2339: Property 'likes' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(93,21): error TS2339: Property 'replyCount' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(93,43): error TS2339: Property 'replyCount' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(118,63): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(126,42): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(145,21): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(339,33): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(343,59): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(344,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(348,34): error TS2339: Property 'userAvatarUrl' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(349,34): error TS2339: Property 'userName' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(355,34): error TS2339: Property 'userName' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(359,52): error TS2339: Property 'timestamp' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(363,32): error TS2339: Property 'commentText' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(367,38): error TS2339: Property 'likes' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(368,32): error TS2339: Property 'replyCount' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(368,61): error TS2339: Property 'replyCount' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(381,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(388,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(395,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/moderation/pages/CommentModerationPage.tsx(402,65): error TS2339: Property 'id' does not exist on type 'CommentWithVideo'.
src/features/playlist/pages/PlaylistDetailPage.tsx(8,36): error TS2307: Cannot find module '../components/LoadingStates/PlaylistDetailSkeleton' or its corresponding type declarations.
src/features/playlist/pages/PlaylistDetailPage.tsx(9,31): error TS2307: Cannot find module '../components/PlaylistEditModal' or its corresponding type declarations.
src/features/playlist/pages/PlaylistDetailPage.tsx(10,89): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/playlist/pages/PlaylistDetailPage.tsx(12,42): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/playlist/pages/PlaylistDetailPage.tsx(96,106): error TS2339: Property 'title' does not exist on type 'PlaylistWithVideos'.
src/features/playlist/pages/PlaylistDetailPage.tsx(111,33): error TS2551: Property 'videoIds' does not exist on type 'PlaylistWithVideos'. Did you mean 'videos'?
src/features/playlist/pages/PlaylistDetailPage.tsx(111,49): error TS7006: Parameter 'id' implicitly has an 'any' type.
src/features/playlist/pages/PlaylistDetailPage.tsx(124,49): error TS2339: Property 'title' does not exist on type 'PlaylistWithVideos'.
src/features/playlist/pages/PlaylistDetailPage.tsx(125,55): error TS2339: Property 'description' does not exist on type 'PlaylistWithVideos'.
src/features/playlist/pages/PlaylistDetailPage.tsx(165,11): error TS2339: Property 'title' does not exist on type 'PlaylistWithVideos'.
src/features/playlist/pages/PlaylistDetailPage.tsx(165,18): error TS2339: Property 'description' does not exist on type 'PlaylistWithVideos'.
src/features/playlist/pages/PlaylistDetailPage.tsx(165,39): error TS2339: Property 'updatedAt' does not exist on type 'PlaylistWithVideos'.
src/features/playlist/pages/PlaylistDetailPage.tsx(166,38): error TS2551: Property 'videoIds' does not exist on type 'PlaylistWithVideos'. Did you mean 'videos'?
src/features/playlist/pages/PlaylistsPage.tsx(8,54): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/playlist/pages/PlaylistsPage.tsx(10,42): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/studio/pages/StudioDashboardPage.tsx(18,37): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/studio/pages/StudioDashboardPage.tsx(19,46): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/studio/pages/StudioPage.tsx(21,58): error TS2307: Cannot find module '../components/ui/Tabs' or its corresponding type declarations.
src/features/studio/pages/StudioPage.tsx(22,31): error TS2307: Cannot find module '../components/ui/UnifiedButton' or its corresponding type declarations.
src/features/user/pages/HistoryPage.tsx(5,25): error TS2307: Cannot find module '../components/icons/HistoryIcon' or its corresponding type declarations.
src/features/user/pages/HistoryPage.tsx(6,33): error TS2307: Cannot find module '../components/LoadingStates/HistoryPageSkeleton' or its corresponding type declarations.
src/features/user/pages/HistoryPage.tsx(7,39): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/user/pages/HistoryPage.tsx(8,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/user/pages/HistoryPage.tsx(10,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/user/pages/LibraryPage.tsx(9,30): error TS2307: Cannot find module '../components/icons/HistoryIcon' or its corresponding type declarations.
src/features/user/pages/LibraryPage.tsx(10,32): error TS2307: Cannot find module '../components/icons/PlaylistIcon' or its corresponding type declarations.
src/features/user/pages/LibraryPage.tsx(16,8): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/user/pages/LibraryPage.tsx(17,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/user/pages/LibraryPage.tsx(19,49): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/user/pages/LibraryPage.tsx(109,38): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/user/pages/LibraryPage.tsx(112,36): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/user/pages/LibraryPage.tsx(115,33): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/user/pages/LibraryPage.tsx(116,46): error TS7006: Parameter 'a' implicitly has an 'any' type.
src/features/user/pages/LibraryPage.tsx(116,49): error TS7006: Parameter 'b' implicitly has an 'any' type.
src/features/user/pages/LibraryPage.tsx(121,31): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/user/pages/LikedVideosPage.tsx(7,37): error TS2307: Cannot find module '../components/LoadingStates/LikedVideosPageSkeleton' or its corresponding type declarations.
src/features/user/pages/LikedVideosPage.tsx(8,32): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/user/pages/LikedVideosPage.tsx(9,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/user/pages/LikedVideosPage.tsx(11,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/user/pages/SettingsPage.tsx(8,26): error TS2307: Cannot find module '../contexts/ThemeContext' or its corresponding type declarations.
src/features/user/pages/SubscriptionsPage.tsx(14,31): error TS2307: Cannot find module '../components/icons/SubscriptionsIcon' or its corresponding type declarations.
src/features/user/pages/SubscriptionsPage.tsx(15,31): error TS2307: Cannot find module '../components/SubscriptionStats' or its corresponding type declarations.
src/features/user/pages/SubscriptionsPage.tsx(16,35): error TS2307: Cannot find module '../components/SubscriptionVideoCard' or its corresponding type declarations.
src/features/user/pages/SubscriptionsPage.tsx(17,24): error TS2307: Cannot find module '../components/ui/Button' or its corresponding type declarations.
src/features/user/pages/SubscriptionsPage.tsx(18,28): error TS2307: Cannot find module '../components/ui/LoadingSpinner' or its corresponding type declarations.
src/features/user/pages/SubscriptionsPage.tsx(19,58): error TS2307: Cannot find module '../components/ui/Tabs' or its corresponding type declarations.
src/features/user/pages/SubscriptionsPage.tsx(20,56): error TS2307: Cannot find module '../hooks' or its corresponding type declarations.
src/features/user/pages/SubscriptionsPage.tsx(101,60): error TS7006: Parameter 'c' implicitly has an 'any' type.
src/features/user/pages/SubscriptionsPage.tsx(107,53): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/user/pages/SubscriptionsPage.tsx(219,39): error TS7006: Parameter 'channel' implicitly has an 'any' type.
src/features/user/pages/SubscriptionsPage.tsx(279,49): error TS7006: Parameter 'value' implicitly has an 'any' type.
src/features/user/pages/WatchLaterPage.tsx(6,31): error TS2307: Cannot find module '../contexts/WatchLaterContext' or its corresponding type declarations.
src/features/user/pages/WatchLaterPage.tsx(7,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/user/pages/WatchLaterPage.tsx(9,28): error TS2307: Cannot find module '../types' or its corresponding type declarations.
src/features/user/pages/YourDataPage.tsx(7,98): error TS2307: Cannot find module '../services/mockVideoService' or its corresponding type declarations.
src/features/video/components/index.ts(2,38): error TS2307: Cannot find module './VideoCard' or its corresponding type declarations.
src/features/video/components/index.ts(4,38): error TS2307: Cannot find module './VideoGrid' or its corresponding type declarations.
src/features/video/components/StudioVideoGrid.tsx(90,16): error TS18048: 'bViews' is possibly 'undefined'.
src/features/video/components/StudioVideoGrid.tsx(90,25): error TS18048: 'aViews' is possibly 'undefined'.
src/features/video/components/StudioVideoGrid.tsx(219,30): error TS18048: 'video.views' is possibly 'undefined'.
src/features/video/components/VideoList.tsx(3,27): error TS2307: Cannot find module '../../components' or its corresponding type declarations.
src/features/video/mocks/videoMocks.ts(112,3): error TS2739: Type '{ id: string; title: string; description: string; thumbnailUrl: string; videoUrl: string; duration: string; views: string; likes: number; dislikes: number; category: string; tags: string[]; createdAt: string; ... 5 more ...; channelAvatarUrl: string; }' is missing the following properties from type 'Video': viewCount, likeCount, dislikeCount, commentCount, publishedAt
src/features/video/pages/HomePage.tsx(7,27): error TS2307: Cannot find module '../components/CategoryChips' or its corresponding type declarations.
src/features/video/pages/HomePage.tsx(8,25): error TS2307: Cannot find module '../components/HomeContent' or its corresponding type declarations.
src/features/video/pages/HomePage.tsx(9,24): error TS2307: Cannot find module '../components/PageLayout' or its corresponding type declarations.
src/features/video/pages/HomePage.tsx(10,10): error TS2724: '"../hooks"' has no exported member named 'useVideos'. Did you mean 'useVideo'?
src/features/video/pages/HomePage.tsx(25,26): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/video/pages/HomePage.tsx(53,9): error TS7006: Parameter 'data' implicitly has an 'any' type.
src/features/video/pages/SearchResultsPage.tsx(8,29): error TS2307: Cannot find module '../hooks/useDebounce' or its corresponding type declarations.
src/features/video/pages/SearchResultsPage.tsx(9,30): error TS2307: Cannot find module '../services/api' or its corresponding type declarations.
src/features/video/pages/SearchResultsPage.tsx(10,83): error TS2307: Cannot find module '../services/googleSearchService' or its corresponding type declarations.
src/features/video/pages/SearchResultsPage.tsx(11,31): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/video/pages/SearchResultsPage.tsx(12,36): error TS2307: Cannot find module '../utils/performance' or its corresponding type declarations.
src/features/video/pages/SearchResultsPage.tsx(81,10): error TS7006: Parameter 'query' implicitly has an 'any' type.
src/features/video/pages/SearchResultsPage.tsx(81,58): error TS7006: Parameter 'result' implicitly has an 'any' type.
src/features/video/pages/SearchResultsPage.tsx(145,24): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/video/pages/ShortsPage.tsx(11,26): error TS2307: Cannot find module '../components/CommentModal' or its corresponding type declarations.
src/features/video/pages/ShortsPage.tsx(12,30): error TS2307: Cannot find module '../components/ErrorStates/EmptyShortsState' or its corresponding type declarations.
src/features/video/pages/ShortsPage.tsx(13,29): error TS2307: Cannot find module '../components/ErrorStates/ShortsPageError' or its corresponding type declarations.
src/features/video/pages/ShortsPage.tsx(14,32): error TS2307: Cannot find module '../components/LoadingStates/ShortsPageSkeleton' or its corresponding type declarations.
src/features/video/pages/ShortsPage.tsx(15,30): error TS2307: Cannot find module '../components/ShortDisplayCard' or its corresponding type declarations.
src/features/video/pages/ShortsPage.tsx(16,27): error TS2307: Cannot find module '../components/ShortsFilters' or its corresponding type declarations.
src/features/video/pages/ShortsPage.tsx(17,30): error TS2307: Cannot find module '../components/ShortsNavigation' or its corresponding type declarations.
src/features/video/pages/ShortsPage.tsx(18,10): error TS2305: Module '"../hooks"' has no exported member 'useShortsVideos'.
src/features/video/pages/ShortsPage.tsx(18,27): error TS2305: Module '"../hooks"' has no exported member 'useLocalStorage'.
src/features/video/pages/ShortsPage.tsx(18,44): error TS2305: Module '"../hooks"' has no exported member 'useDebounce'.
src/features/video/pages/ShortsPage.tsx(20,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/features/video/pages/ShortsPage.tsx(84,15): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/video/pages/ShortsPage.tsx(85,12): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/video/pages/ShortsPage.tsx(123,56): error TS7006: Parameter 'short' implicitly has an 'any' type.
src/features/video/pages/ShortsPage.tsx(129,25): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/video/pages/ShortsPage.tsx(140,30): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/features/video/pages/TrendingPage.tsx(7,26): error TS2307: Cannot find module '../components/CategoryTabs' or its corresponding type declarations.
src/features/video/pages/TrendingPage.tsx(8,24): error TS2307: Cannot find module '../components/PageLayout' or its corresponding type declarations.
src/features/video/pages/TrendingPage.tsx(10,27): error TS2307: Cannot find module '../src/components' or its corresponding type declarations.
src/features/video/pages/TrendingPage.tsx(14,33): error TS2339: Property 'loading' does not exist on type 'UseQueryResult<Video[], Error>'.
src/features/video/pages/TrendingPage.tsx(37,9): error TS7006: Parameter 'videos' implicitly has an 'any' type.
src/features/video/pages/TrendingPage.tsx(39,31): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/features/video/pages/WatchPage.tsx(4,10): error TS2305: Module '"../components"' has no exported member 'AdvancedVideoPlayer'.
src/features/video/pages/WatchPage.tsx(4,31): error TS2305: Module '"../components"' has no exported member 'YouTubePlayer'.
src/features/video/pages/WatchPage.tsx(4,46): error TS2305: Module '"../components"' has no exported member 'YouTubePlayerWrapper'.
src/features/video/pages/WatchPage.tsx(4,68): error TS2305: Module '"../components"' has no exported member 'VideoDescription'.
src/features/video/pages/WatchPage.tsx(4,86): error TS2305: Module '"../components"' has no exported member 'VideoActions'.
src/features/video/pages/WatchPage.tsx(4,100): error TS2305: Module '"../components"' has no exported member 'CommentsSection'.
src/features/video/pages/WatchPage.tsx(4,117): error TS2305: Module '"../components"' has no exported member 'RefactoredSaveToPlaylistModal'.
src/features/video/pages/WatchPage.tsx(4,148): error TS2305: Module '"../components"' has no exported member 'RecommendationEngine'.
src/features/video/pages/WatchPage.tsx(5,27): error TS2307: Cannot find module '../components/VideoMetadata' or its corresponding type declarations.
src/features/video/pages/WatchPage.tsx(6,38): error TS2307: Cannot find module '../contexts/OptimizedMiniplayerContext' or its corresponding type declarations.
src/features/video/pages/WatchPage.tsx(7,31): error TS2307: Cannot find module '../contexts/WatchLaterContext' or its corresponding type declarations.
src/features/video/pages/WatchPage.tsx(8,30): error TS2307: Cannot find module '../hooks/useWatchPage' or its corresponding type declarations.
src/features/video/pages/WatchPage.tsx(9,38): error TS2307: Cannot find module '../services/settingsService' or its corresponding type declarations.
src/features/video/pages/WatchPage.tsx(10,49): error TS2307: Cannot find module '../src/lib/youtube-utils' or its corresponding type declarations.
src/features/video/pages/WatchPage.tsx(11,37): error TS2307: Cannot find module '../utils/dateUtils' or its corresponding type declarations.
src/features/video/pages/WatchPage.tsx(12,29): error TS2307: Cannot find module '../utils/numberUtils' or its corresponding type declarations.
src/features/video/pages/WatchPage.tsx(316,31): error TS7006: Parameter 'videoId' implicitly has an 'any' type.
src/features/video/services/__tests__/videoService.test.ts(5,28): error TS2307: Cannot find module '../../../types/video' or its corresponding type declarations.
src/features/video/services/videoService.ts(40,74): error TS2379: Argument of type '{ category: string | undefined; }' is not assignable to parameter of type 'UnifiedSearchFilters' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'category' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/features/video/services/videoService.ts(261,7): error TS2322: Type 'Video | null' is not assignable to type 'Video'.
  Type 'null' is not assignable to type 'Video'.
src/hooks/unified/__tests__/useVideos.test.tsx(31,9): error TS2353: Object literal may only specify known properties, and 'cacheTime' does not exist in type 'OmitKeyof<QueryObserverOptions<unknown, Error, unknown, unknown, readonly unknown[], never>, "suspense" | "queryKey", "strictly">'.
src/hooks/unified/__tests__/useVideos.test.tsx(65,66): error TS2345: Argument of type '{ data: { id: string; title: string; source: string; }[]; sources: { local: { count: number; hasMore: boolean; }; youtube: { count: number; hasMore: boolean; }; }; totalCount: number; hasMore: boolean; }' is not assignable to parameter of type 'UnifiedDataResponse<UnifiedVideoMetadata>'.
  Types of property 'data' are incompatible.
    Type '{ id: string; title: string; source: string; }[]' is not assignable to type 'UnifiedVideoMetadata[]'.
      Type '{ id: string; title: string; source: string; }' is missing the following properties from type 'UnifiedVideoMetadata': description, thumbnailUrl, videoUrl, views, and 14 more.
src/hooks/unified/__tests__/useVideos.test.tsx(78,35): error TS2339: Property 'data' does not exist on type 'UnifiedVideoMetadata[]'.
src/hooks/unified/__tests__/useVideos.test.tsx(79,35): error TS2339: Property 'success' does not exist on type 'UnifiedVideoMetadata[]'.
src/hooks/unified/__tests__/useVideos.test.tsx(136,61): error TS2345: Argument of type '{ id: string; title: string; description: string; source: string; }' is not assignable to parameter of type 'UnifiedVideoMetadata'.
  Type '{ id: string; title: string; description: string; source: string; }' is missing the following properties from type 'UnifiedVideoMetadata': thumbnailUrl, videoUrl, views, viewsFormatted, and 13 more.
src/hooks/unified/__tests__/useVideos.test.tsx(149,35): error TS2339: Property 'data' does not exist on type 'UnifiedVideoMetadata'.
src/hooks/unified/__tests__/useVideos.test.tsx(150,35): error TS2339: Property 'success' does not exist on type 'UnifiedVideoMetadata'.
src/hooks/unified/__tests__/useVideos.test.tsx(216,66): error TS2345: Argument of type '{ data: { id: string; title: string; }[]; sources: { local: { count: number; hasMore: boolean; }; youtube: { count: number; hasMore: boolean; }; }; totalCount: number; hasMore: boolean; }' is not assignable to parameter of type 'UnifiedDataResponse<UnifiedVideoMetadata>'.
  Types of property 'data' are incompatible.
    Type '{ id: string; title: string; }[]' is not assignable to type 'UnifiedVideoMetadata[]'.
      Type '{ id: string; title: string; }' is missing the following properties from type 'UnifiedVideoMetadata': description, thumbnailUrl, videoUrl, views, and 15 more.
src/hooks/unified/__tests__/useVideos.test.tsx(229,35): error TS2339: Property 'data' does not exist on type 'UnifiedVideoMetadata[]'.
src/hooks/unified/__tests__/useVideos.test.tsx(272,64): error TS2345: Argument of type '{ data: { id: string; title: string; isShort: boolean; }[]; sources: { local: { count: number; hasMore: boolean; }; youtube: { count: number; hasMore: boolean; }; }; totalCount: number; hasMore: boolean; }' is not assignable to parameter of type 'UnifiedDataResponse<UnifiedVideoMetadata>'.
  Types of property 'data' are incompatible.
    Type '{ id: string; title: string; isShort: boolean; }[]' is not assignable to type 'UnifiedVideoMetadata[]'.
      Type '{ id: string; title: string; isShort: boolean; }' is missing the following properties from type 'UnifiedVideoMetadata': description, thumbnailUrl, videoUrl, views, and 14 more.
src/hooks/unified/__tests__/useVideos.test.tsx(285,35): error TS2339: Property 'data' does not exist on type 'UnifiedVideoMetadata[]'.
src/hooks/unified/__tests__/useVideos.test.tsx(304,61): error TS2345: Argument of type '{ data: { id: string; title: string; }[]; sources: { local: { count: number; hasMore: boolean; }; youtube: { count: number; hasMore: boolean; }; }; totalCount: number; hasMore: boolean; }' is not assignable to parameter of type 'UnifiedDataResponse<UnifiedVideoMetadata>'.
  Types of property 'data' are incompatible.
    Type '{ id: string; title: string; }[]' is not assignable to type 'UnifiedVideoMetadata[]'.
      Type '{ id: string; title: string; }' is missing the following properties from type 'UnifiedVideoMetadata': description, thumbnailUrl, videoUrl, views, and 15 more.
src/hooks/unified/__tests__/useVideos.test.tsx(321,35): error TS2339: Property 'data' does not exist on type 'UnifiedVideoMetadata[]'.
src/hooks/unified/__tests__/useVideos.test.tsx(377,15): error TS6133: 'mockVideoApi' is declared but its value is never read.
src/hooks/unified/useVideos.ts(21,24): error TS2322: Type 'number' is not assignable to type 'string'.
src/hooks/unified/useVideos.ts(36,5): error TS2345: Argument of type '() => Promise<{ data: UnifiedVideoMetadata; success: true; message: string; } | null>' is not assignable to parameter of type '() => Promise<ApiResponse<UnifiedVideoMetadata>>'.
  Type 'Promise<{ data: UnifiedVideoMetadata; success: true; message: string; } | null>' is not assignable to type 'Promise<ApiResponse<UnifiedVideoMetadata>>'.
    Type '{ data: UnifiedVideoMetadata; success: true; message: string; } | null' is not assignable to type 'ApiResponse<UnifiedVideoMetadata>'.
      Type 'null' is not assignable to type 'ApiResponse<UnifiedVideoMetadata>'.
src/hooks/unified/useVideos.ts(54,26): error TS2322: Type 'number' is not assignable to type 'string'.
src/hooks/useOptimizedVideoData.ts(164,32): error TS2379: Argument of type '{ category: string | undefined; limit: number; enableCache: true; }' is not assignable to parameter of type 'UseVideoDataOptions' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'category' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/hooks/useVideoData.ts(9,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/hooks/useVideosData.ts(4,36): error TS2307: Cannot find module '../src/services/unifiedDataService' or its corresponding type declarations.
src/hooks/useVideosData.ts(8,43): error TS2307: Cannot find module '../src/services/metadataNormalizationService' or its corresponding type declarations.
src/hooks/useVideosData.ts(9,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/hooks/useWatchPage.ts(6,36): error TS2307: Cannot find module '../src/services/unifiedDataService' or its corresponding type declarations.
src/hooks/useWatchPage.ts(155,13): error TS6133: 'isFromUnifiedService' is declared but its value is never read.
src/services/__tests__/analyticsService.test.ts(10,15): error TS2614: Module '"../analyticsService"' has no exported member 'AnalyticsData'. Did you mean to use 'import AnalyticsData from "../analyticsService"' instead?
src/services/__tests__/analyticsService.test.ts(33,44): error TS2345: Argument of type '{ data: AnalyticsData; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: AnalyticsData; }' but required in type 'ApiResponse<unknown>'.
src/services/__tests__/analyticsService.test.ts(35,45): error TS2339: Property 'fetchVideoAnalytics' does not exist on type 'AnalyticsService'.
src/services/__tests__/analyticsService.test.ts(46,37): error TS2339: Property 'fetchVideoAnalytics' does not exist on type 'AnalyticsService'.
src/services/__tests__/analyticsService.test.ts(58,44): error TS2345: Argument of type '{ data: AnalyticsData; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: AnalyticsData; }' but required in type 'ApiResponse<unknown>'.
src/services/__tests__/analyticsService.test.ts(60,45): error TS2339: Property 'fetchVideoAnalytics' does not exist on type 'AnalyticsService'.
src/services/__tests__/searchService.test.ts(141,44): error TS2345: Argument of type '{ data: ({ query: string; type: "query"; } | { query: string; type: "trending"; })[]; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: ({ query: string; type: "query"; } | { query: string; type: "trending"; })[]; }' but required in type 'ApiResponse<unknown>'.
src/services/__tests__/searchService.test.ts(168,44): error TS2345: Argument of type '{ data: string[]; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: string[]; }' but required in type 'ApiResponse<unknown>'.
src/services/__tests__/searchService.test.ts(224,47): error TS2345: Argument of type '{ data: Video[]; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: Video[]; }' but required in type 'ApiResponse<unknown>'.
src/services/__tests__/searchService.test.ts(237,47): error TS2345: Argument of type '{ data: null; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: null; }' but required in type 'ApiResponse<unknown>'.
src/services/__tests__/unifiedDataService.test.ts(3,35): error TS2307: Cannot find module '../../../services/mockVideoService' or its corresponding type declarations.
src/services/__tests__/unifiedDataService.test.ts(41,33): error TS2304: Cannot find name 'UnifiedDataService'.
src/services/__tests__/unifiedDataService.test.ts(55,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(60,22): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(67,11): error TS6133: 'mockLocalVideos' is declared but its value is never read.
src/services/__tests__/unifiedDataService.test.ts(72,11): error TS6133: 'mockYoutubeVideos' is declared but its value is never read.
src/services/__tests__/unifiedDataService.test.ts(94,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(107,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(118,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(125,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(135,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(145,13): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(149,13): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(155,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(157,13): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(163,13): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(181,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(193,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(205,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(214,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(230,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(241,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(252,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(261,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(268,13): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(272,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(287,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(298,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(309,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(317,45): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(319,13): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(337,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(340,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(350,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(357,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(367,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(376,28): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(389,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(390,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(393,22): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(399,8): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(400,8): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(401,8): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(403,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(406,15): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(411,7): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(413,8): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(416,15): error TS2304: Cannot find name 'service'.
src/services/__tests__/unifiedDataService.test.ts(422,15): error TS2304: Cannot find name 'service'.
src/services/__tests__/uploadService.test.ts(32,47): error TS2345: Argument of type '{ data: UploadResponse; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: UploadResponse; }' but required in type 'ApiResponse<unknown>'.
src/services/__tests__/uploadService.test.ts(64,47): error TS2345: Argument of type '{ data: UploadResponse; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: UploadResponse; }' but required in type 'ApiResponse<unknown>'.
src/services/__tests__/uploadService.test.ts(83,47): error TS2345: Argument of type '{ data: UploadResponse; }' is not assignable to parameter of type 'ApiResponse<unknown>'.
  Property 'success' is missing in type '{ data: UploadResponse; }' but required in type 'ApiResponse<unknown>'.
src/services/adapters/__tests__/videoAdapter.test.ts(96,13): error TS2741: Property 'videoUrl' is missing in type '{ id: string; title: string; description: string; thumbnailUrl: string; views: number; viewsFormatted: string; likes: number; dislikes: number; commentCount: number; channel: { id: string; name: string; avatarUrl: string; subscribers: number; subscribersFormatted: string; isVerified: false; }; ... 9 more ...; metada...' but required in type 'UnifiedVideoMetadata'.
src/services/adapters/__tests__/videoAdapter.test.ts(137,14): error TS18048: 'result.channel' is possibly 'undefined'.
src/services/adapters/__tests__/videoAdapter.test.ts(147,14): error TS2532: Object is possibly 'undefined'.
src/services/adapters/__tests__/videoAdapter.test.ts(148,14): error TS2532: Object is possibly 'undefined'.
src/services/adapters/__tests__/videoAdapter.test.ts(159,14): error TS2532: Object is possibly 'undefined'.
src/services/api.ts(210,3): error TS2353: Object literal may only specify known properties, and 'monetization' does not exist in type 'Video'.
src/services/api.ts(359,9): error TS2353: Object literal may only specify known properties, and 'monetization' does not exist in type 'Video'.
src/services/api/__tests__/youtubeService.test.ts(124,18): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(136,16): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(225,18): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(309,19): error TS2540: Cannot assign to 'env' because it is a read-only property.
src/services/api/__tests__/youtubeService.test.ts(477,23): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(499,23): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(538,14): error TS2304: Cannot find name 'mockYouTubeVideoResponse'.
src/services/api/__tests__/youtubeService.test.ts(540,16): error TS2304: Cannot find name 'mockYouTubeVideoResponse'.
src/services/api/__tests__/youtubeService.test.ts(552,14): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(558,14): error TS2304: Cannot find name 'mockYouTubeVideoResponse'.
src/services/api/__tests__/youtubeService.test.ts(569,14): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(570,14): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(571,14): error TS2532: Object is possibly 'undefined'.
src/services/api/__tests__/youtubeService.test.ts(577,14): error TS2304: Cannot find name 'mockYouTubeVideoResponse'.
src/services/api/__tests__/youtubeService.test.ts(579,16): error TS2304: Cannot find name 'mockYouTubeVideoResponse'.
src/services/api/__tests__/youtubeService.test.ts(591,14): error TS2532: Object is possibly 'undefined'.
src/services/api/youtubeService.ts(136,13): error TS2322: Type '{ id: string; title: string; description: string; thumbnailUrl: string; duration: string; views: string; viewCount: number; likes: number; likeCount: number; dislikes: number; dislikeCount: number; ... 21 more ...; metadata: { ...; }; }[]' is not assignable to type 'Video[]'.
  Type '{ id: string; title: string; description: string; thumbnailUrl: string; duration: string; views: string; viewCount: number; likes: number; likeCount: number; dislikes: number; dislikeCount: number; ... 21 more ...; metadata: { ...; }; }' is not assignable to type 'Video'.
    The types of 'metadata.defaultLanguage' are incompatible between these types.
      Type 'string | undefined' is not assignable to type 'string'.
        Type 'undefined' is not assignable to type 'string'.
src/services/api/youtubeService.ts(166,27): error TS2339: Property 'liveBroadcastContent' does not exist on type '{ title: string; description: string; thumbnails: { medium: { url: string; }; high?: { url: string; }; }; publishedAt: string; channelId: string; channelTitle: string; tags?: string[]; categoryId: string; defaultLanguage?: string; }'.
src/services/api/youtubeService.ts(192,45): error TS2339: Property 'licensedContent' does not exist on type '{ duration: string; dimension: string; definition: string; caption: string; }'.
src/services/api/youtubeService.ts(193,43): error TS2339: Property 'contentRating' does not exist on type '{ duration: string; dimension: string; definition: string; caption: string; }'.
src/services/api/youtubeService.ts(194,40): error TS2339: Property 'projection' does not exist on type '{ duration: string; dimension: string; definition: string; caption: string; }'.
src/services/api/youtubeService.ts(249,11): error TS18048: 'video.channel' is possibly 'undefined'.
src/services/api/youtubeService.ts(250,11): error TS18048: 'video.channel' is possibly 'undefined'.
src/services/api/youtubeService.ts(303,13): error TS2375: Type '{ id: string; name: string; description: string; avatarUrl: string; banner: string; subscribers: number; subscriberCount: string; videoCount: number; totalViews: number; isVerified: false; joinedDate: string; country: string | undefined; createdAt: string; updatedAt: string; }' is not assignable to type 'Channel' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'country' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/services/googleSearchService.ts(258,3): error TS2375: Type '{ id: string; title: string; description: string; thumbnailUrl: string; channelName: string; channelId: string; channelAvatarUrl: string | undefined; videoUrl: string; embedUrl: string; duration: string; ... 7 more ...; isYouTube: true; }' is not assignable to type 'YouTubeSearchResult' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'channelAvatarUrl' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/services/googleSearchService.ts(298,3): error TS2375: Type '{ id: string; title: string; description: string; thumbnailUrl: string; channelName: string; channelId: string | undefined; channelAvatarUrl: string | undefined; videoUrl: string; embedUrl: string; ... 9 more ...; source: "google-search"; }' is not assignable to type 'GoogleSearchResult' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'channelId' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/services/index.ts(21,15): error TS2614: Module '"./analyticsService"' has no exported member 'AnalyticsData'. Did you mean to use 'import AnalyticsData from "./analyticsService"' instead?
src/services/metadataNormalizationService.ts(104,7): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/services/metadataNormalizationService.ts(127,7): error TS2375: Type '{ quality: string | undefined; definition: string | undefined; captions: boolean; language: string | undefined; license: string | undefined; }' is not assignable to type '{ quality?: string; definition?: string; captions?: boolean; language?: string; license?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'quality' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/services/metadataNormalizationService.ts(214,5): error TS2322: Type '{ id: any; title: any; description: any; thumbnailUrl: string; videoUrl: string; views: number; viewsFormatted: string; likes: number; dislikes: number; commentCount: number; channel: { id: any; name: any; avatarUrl: any; subscribers: any; subscribersFormatted: string; isVerified: any; }; ... 9 more ...; metadata: {...' is not assignable to type 'UnifiedVideoMetadata'.
  Types of property 'source' are incompatible.
    Type 'string' is not assignable to type '"local" | "youtube" | "external"'.
src/services/metadataNormalizationService.ts(221,5): error TS2375: Type '{ id: string; name: string; handle: string | undefined; description: string; avatarUrl: string; bannerUrl: string | undefined; subscribers: number; subscribersFormatted: string; videoCount: number; ... 4 more ...; source: "local"; }' is not assignable to type 'UnifiedChannelMetadata' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'handle' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/services/mockVideoService.ts(3,46): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/services/searchService.ts(56,9): error TS2412: Type '"short" | "medium" | "long" | "any" | undefined' is not assignable to type '"short" | "medium" | "long" | undefined' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
src/services/searchService.ts(57,9): error TS2412: Type '"hour" | "today" | "week" | "month" | "year" | "any" | undefined' is not assignable to type '"hour" | "today" | "week" | "month" | "year" | undefined' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
src/services/searchService.ts(58,9): error TS2412: Type '"title" | "view_count" | "relevance" | "upload_date" | "rating" | undefined' is not assignable to type '"relevance" | "rating" | "date" | "views" | undefined' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
src/services/searchService.ts(112,5): error TS6133: 'page' is declared but its value is never read.
src/services/searchService.ts(205,7): error TS2322: Type 'unknown' is not assignable to type '{ query: string; timestamp: string; filters?: SearchFilters; }[]'.
src/services/unifiedApiService.ts(1,31): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/services/unifiedDataService.ts(1,35): error TS2307: Cannot find module '../../services/mockVideoService' or its corresponding type declarations.
src/services/unifiedDataService.ts(271,19): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(272,22): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(273,28): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(274,29): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(275,15): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/services/unifiedDataService.ts(275,25): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(276,22): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(277,48): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(278,22): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(279,25): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(280,29): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(282,21): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(283,23): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(284,28): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(284,63): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(287,29): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(289,15): error TS2322: Type 'string | number' is not assignable to type 'string'.
  Type 'number' is not assignable to type 'string'.
src/services/unifiedDataService.ts(289,25): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(290,28): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(291,56): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(292,25): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(293,21): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(294,15): error TS2322: Type 'boolean | undefined' is not assignable to type 'boolean'.
  Type 'undefined' is not assignable to type 'boolean'.
src/services/unifiedDataService.ts(294,23): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(295,15): error TS2322: Type 'boolean | undefined' is not assignable to type 'boolean'.
  Type 'undefined' is not assignable to type 'boolean'.
src/services/unifiedDataService.ts(295,24): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(296,15): error TS2322: Type 'VideoVisibility | undefined' is not assignable to type '"public" | "unlisted" | "private" | "scheduled"'.
  Type 'undefined' is not assignable to type '"public" | "unlisted" | "private" | "scheduled"'.
src/services/unifiedDataService.ts(296,27): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(336,19): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(337,22): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(338,28): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(339,29): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(340,15): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/services/unifiedDataService.ts(340,25): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(341,22): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(342,48): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(343,22): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(344,25): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(345,29): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(347,21): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(348,23): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(349,17): error TS2322: Type 'string | undefined' is not assignable to type 'string'.
  Type 'undefined' is not assignable to type 'string'.
src/services/unifiedDataService.ts(349,28): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(352,29): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(354,15): error TS2322: Type 'string | number' is not assignable to type 'string'.
  Type 'number' is not assignable to type 'string'.
src/services/unifiedDataService.ts(354,25): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(355,28): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(356,56): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(357,25): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(358,21): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(359,15): error TS2322: Type 'boolean | undefined' is not assignable to type 'boolean'.
  Type 'undefined' is not assignable to type 'boolean'.
src/services/unifiedDataService.ts(359,23): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(360,15): error TS2322: Type 'boolean | undefined' is not assignable to type 'boolean'.
  Type 'undefined' is not assignable to type 'boolean'.
src/services/unifiedDataService.ts(360,24): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(361,15): error TS2322: Type 'VideoVisibility | undefined' is not assignable to type '"public" | "unlisted" | "private" | "scheduled"'.
  Type 'undefined' is not assignable to type '"public" | "unlisted" | "private" | "scheduled"'.
src/services/unifiedDataService.ts(361,27): error TS18048: 'processedVideo' is possibly 'undefined'.
src/services/unifiedDataService.ts(481,48): error TS7006: Parameter 'v' implicitly has an 'any' type.
src/services/unifiedDataService.ts(483,48): error TS7006: Parameter 'v' implicitly has an 'any' type.
src/services/unifiedDataService.ts(487,48): error TS7006: Parameter 'v' implicitly has an 'any' type.
src/services/unifiedDataService.ts(492,33): error TS7006: Parameter 'video' implicitly has an 'any' type.
src/services/unifiedDataService.ts(542,20): error TS2345: Argument of type 'UnifiedVideoMetadata | undefined' is not assignable to parameter of type 'UnifiedVideoMetadata'.
  Type 'undefined' is not assignable to type 'UnifiedVideoMetadata'.
src/services/unifiedDataService.ts(545,20): error TS2345: Argument of type 'UnifiedVideoMetadata | undefined' is not assignable to parameter of type 'UnifiedVideoMetadata'.
  Type 'undefined' is not assignable to type 'UnifiedVideoMetadata'.
src/services/unifiedDataService.ts(713,34): error TS2484: Export declaration conflicts with exported declaration of 'UnifiedSearchFilters'.
src/services/unifiedDataService.ts(713,56): error TS2484: Export declaration conflicts with exported declaration of 'UnifiedDataResponse'.
src/store/index.ts(5,37): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/types/core.ts(61,28): error TS1149: File name 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/Video.ts' differs from already included file name 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/video.ts' only in casing.
  The file is in the program because:
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts'
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts'
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts'
    Imported via './Video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/core.ts'
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts'
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/legacy.ts'
    Imported via '../types/Video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/normalizeVideo.ts'
    Imported via '../../../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/subscription/services/subscriptionService.ts'
    Imported via '../../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/adapters/videoAdapter.ts'
    Imported via '../../../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/services/videoService.ts'
    Imported via '../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/searchService.ts'
    Imported via '../../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/searchService.test.ts'
    Matched by include pattern 'src/**/*' in 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/tsconfig.json'
src/types/core.ts(86,38): error TS2304: Cannot find name 'Video'.
src/types/core.ts(142,37): error TS2304: Cannot find name 'Video'.
src/types/core.ts(285,9): error TS2304: Cannot find name 'Video'.
src/types/index.ts(48,1): error TS2308: Module './core' has already exported a member named 'BaseEntity'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(49,1): error TS2308: Module './core' has already exported a member named 'Short'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'BaseEntity'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'DemographicData'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'EngagementMetrics'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'FormField'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'MonetizationSettings'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'NotificationSettings'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'PaginationInfo'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'Playlist'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'PlaylistVideo'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'PrivacySettings'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'SearchFilters'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'SearchResult'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'Subscription'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'TrafficSource'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'User'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'UserPreferences'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'ValidationRule'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'VideoAnalytics'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './core' has already exported a member named 'VideoEvent'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './legacy' has already exported a member named 'UploadProgress'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './strictTypes' has already exported a member named 'AnalyticsEvent'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './strictTypes' has already exported a member named 'CommentAuthor'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './strictTypes' has already exported a member named 'DeepPartial'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './strictTypes' has already exported a member named 'RequiredFields'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(51,1): error TS2308: Module './video' has already exported a member named 'Chapter'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(54,1): error TS2308: Module './core' has already exported a member named 'User'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/index.ts(54,1): error TS2308: Module './unifiedTypes' has already exported a member named 'RegisterData'. Consider explicitly re-exporting to resolve the ambiguity.
src/types/legacy.ts(111,26): error TS2304: Cannot find name 'ExtendedVideo'.
src/types/strictTypes.ts(36,43): error TS2307: Cannot find module '../src/types/Video' or its corresponding type declarations.
src/types/unifiedTypes.ts(67,28): error TS2307: Cannot find module '../src/types/Video' or its corresponding type declarations.
src/types/unifiedTypes.ts(547,10): error TS2304: Cannot find name 'Video'.
src/types/unifiedTypes.ts(548,27): error TS2304: Cannot find name 'Video'.
src/utils/analyticsUtils.ts(1,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/utils/mocks/youtubeApiTestHandlers.ts(10,60): error TS7030: Not all code paths return a value.
src/utils/normalizeVideo.ts(8,73): error TS1149: File name 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/Video.ts' differs from already included file name 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/video.ts' only in casing.
  The file is in the program because:
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts'
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts'
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts'
    Imported via './Video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/core.ts'
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts'
    Imported via './video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/legacy.ts'
    Imported via '../types/Video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/normalizeVideo.ts'
    Imported via '../../../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/subscription/services/subscriptionService.ts'
    Imported via '../../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/adapters/videoAdapter.ts'
    Imported via '../../../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/services/videoService.ts'
    Imported via '../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/searchService.ts'
    Imported via '../../types/video' from file 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/searchService.test.ts'
    Matched by include pattern 'src/**/*' in 'C:/Users/<USER>/Documents/GitHub/yt/ytmain5/tsconfig.json'
src/utils/normalizeVideo.ts(256,5): error TS2375: Type '{ duration: string; dimension: string; definition: string; caption: string; licensedContent: boolean; regionRestriction: { allowed?: string[]; blocked?: string[]; } | undefined; contentRating: Record<...> | undefined; projection: string | undefined; }' is not assignable to type 'VideoContentDetails' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'regionRestriction' are incompatible.
    Type '{ allowed?: string[]; blocked?: string[]; } | undefined' is not assignable to type '{ allowed?: string[]; blocked?: string[]; }'.
      Type 'undefined' is not assignable to type '{ allowed?: string[]; blocked?: string[]; }'.
src/utils/normalizeVideo.ts(271,5): error TS2412: Type '{ uploadStatus: string; failureReason: string | undefined; rejectionReason: string | undefined; privacyStatus: string; publishAt: string | undefined; license: string; embeddable: boolean; publicStatsViewable: boolean; madeForKids: boolean; selfDeclaredMadeForKids: boolean | undefined; } | undefined' is not assignable to type 'VideoStatus | undefined' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the type of the target.
src/utils/normalizeVideo.ts(375,34): error TS2345: Argument of type 'ApiVideo' is not assignable to parameter of type 'ApiYouTubeVideo'.
  Type 'ApiExternalVideo' is missing the following properties from type 'ApiYouTubeVideo': snippet, statistics, contentDetails
src/utils/playerUtils.ts(336,36): error TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.
  Type 'number' is not assignable to type 'string'.
src/utils/test-setup.ts(9,7): error TS2300: Duplicate identifier 'testUtils'.
src/utils/test-setup.ts(390,3): error TS2322: Type '() => () => void' is not assignable to type '() => { restore: () => void; }'.
  Type '() => void' is not assignable to type '{ restore: () => void; }'.
src/utils/test-setup.ts(447,9): error TS2300: Duplicate identifier 'testUtils'.
src/utils/testing.tsx(10,37): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
src/utils/videoUtils.ts(1,28): error TS2307: Cannot find module '../src/types/core' or its corresponding type declarations.
test/test-utils.tsx(88,5): error TS2353: Object literal may only specify known properties, and 'logger' does not exist in type 'QueryClientConfig'.
test/test-utils.tsx(118,8): error TS2375: Type '{ children: ReactNode; queryClient: QueryClient | undefined; }' is not assignable to type 'AllTheProvidersProps' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'queryClient' are incompatible.
    Type 'QueryClient | undefined' is not assignable to type 'QueryClient'.
      Type 'undefined' is not assignable to type 'QueryClient'.
test/test-utils.tsx(353,5): error TS2353: Object literal may only specify known properties, and 'logger' does not exist in type 'QueryClientConfig'.
vite.config.proxy.ts(16,33): error TS6133: 'proxyReq' is declared but its value is never read.
