import { describe, it, expect, vi } from 'vitest';

import { videoService } from '../videoService';

import type { Video } from '../../../types/video';

vi.mock('../unifiedDataService', () => ({
  unifiedDataService: {
    getTrendingVideos: vi.fn().mockResolvedValue({ data: [] }),
    getVideoById: vi.fn().mockResolvedValue(null),
    searchVideos: vi.fn().mockResolvedValue({ data: [] }),
  },
}));

const mockVideo: Video = {
  id: 'video-id',
  title: 'Test Title',
  description: 'Test Description',
  thumbnailUrl: 'https://example.com/thumbnail.jpg',
  videoUrl: 'https://example.com/video.mp4',
  duration: '4:20',
  viewCount: 1000,
  views: '1K views',
  likeCount: 100,
  likes: 100,
  dislikeCount: 5,
  dislikes: 5,
  commentCount: 20,
  publishedAt: '2023-01-01T00:00:00Z',
  uploadedAt: '2023-01-01T00:00:00Z',
  channelId: 'channel-id',
  channelName: 'Test Channel',
  channelAvatarUrl: 'https://example.com/avatar.jpg',
  channelTitle: 'Test Channel',
  category: 'Entertainment',
  tags: ['test', 'video'],
  isLive: false,
  isShort: false,
  visibility: 'public',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-02T00:00:00Z',
  channel: {
    id: 'channel-id',
    name: 'Test Channel',
    avatarUrl: 'https://example.com/avatar.jpg',
    isVerified: true,
    subscribers: 10000,
    subscriberCount: '10K',
  },
};

describe('VideoService', () => {
  describe('getTrendingVideos', () => {
    it('should return an empty array if no videos are found', async () => {
      const videos = await videoService.getTrendingVideos();
      expect(videos).toEqual([]);
    });

    it('should return an array of videos', async () => {
      (videoService.getTrendingVideos as any).mockResolvedValueOnce([mockVideo]);
      const videos = await videoService.getTrendingVideos();
      expect(videos).toEqual([mockVideo]);
    });
  });

  describe('getVideo', () => {
    it('should return null if no video is found', async () => {
      const video = await videoService.getVideo('unknown-id');
      expect(video).toBeNull();
    });

    it('should return a video if found', async () => {
      (videoService.getVideo as any).mockResolvedValueOnce(mockVideo);
      const video = await videoService.getVideo('video-id');
      expect(video).toEqual(mockVideo);
    });
  });
});

