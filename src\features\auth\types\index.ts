/**
 * Auth feature type definitions
 * 
 * Re-exports centralized types to maintain backward compatibility
 * while ensuring all types come from the centralized type system.
 */

// Re-export centralized auth and user types
export type {
  User,
  UserRole,
  AuthState,
  LoginCredentials,
  RegisterData,
  AuthTokens,
  AuthContextType,
  UserPreferences,
  NotificationSettings,
  PrivacySettings,
  SocialLinks
} from '@/types/User';
