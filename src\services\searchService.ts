/**
 * Centralized Search Service
 * Handles all search functionality and returns unified Video types
 */

import { unifiedVideosToVideos } from '@/services/adapters/videoAdapter';
import { api } from '@/services/api/base';
import { unifiedDataService } from '@/services/unifiedDataService';
import type { Video } from '@/types/video';

export interface SearchFilters {
  type?: 'all' | 'video' | 'channel' | 'playlist' | 'shorts';
  duration?: 'any' | 'short' | 'medium' | 'long'; // <4min, 4-20min, >20min
  uploadDate?: 'any' | 'hour' | 'today' | 'week' | 'month' | 'year';
  sortBy?: 'relevance' | 'upload_date' | 'view_count' | 'rating' | 'title';
  features?: Array<'live' | 'hd' | '4k' | 'subtitles' | 'creative_commons' | 'vr180' | '360' | 'hdr'>;
  category?: string;
  language?: string;
  region?: string;
  safeSearch?: 'none' | 'moderate' | 'strict';
}

export interface SearchSuggestion {
  query: string;
  type: 'query' | 'video' | 'channel' | 'trending';
  metadata?: {
    videoCount?: number;
    channelName?: string;
    thumbnail?: string;
  };
}

export interface SearchResult {
  videos: Video[];
  totalResults: number;
  nextPageToken?: string;
  searchTime: number;
  relatedQueries: string[];
}

class SearchService {
  /**
   * Search for videos across all sources
   */
  async searchVideos(
    query: string,
    filters: SearchFilters = {},
    limit: number = 20,
  ): Promise<Video[]> {
    try {
      // Use unified data service for cross-platform search
      const response = await unifiedDataService.searchVideos(query, {
        type: filters.type === 'shorts' ? 'short' : 'video',
        category: filters.category,
        duration: filters.duration,
        uploadDate: filters.uploadDate,
        sortBy: filters.sortBy,
      }, limit);

      return unifiedVideosToVideos(response.data);
    } catch (error) {
      console.error('Search failed:', error);
      return [];
    }
  }

  /**
   * Get search suggestions
   */
  async getSearchSuggestions(
    query: string,
    limit: number = 10,
  ): Promise<SearchSuggestion[]> {
    try {
      const response = await api.get<SearchSuggestion[]>('/api/search/suggestions', {
        q: query,
        limit,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get search suggestions:', error);
      return [];
    }
  }

  /**
   * Get trending searches
   */
  async getTrendingSearches(
    region?: string,
    limit: number = 20,
  ): Promise<string[]> {
    try {
      const response = await api.get<string[]>('/api/search/trending', {
        region,
        limit,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get trending searches:', error);
      return [];
    }
  }

  /**
   * Advanced search with comprehensive filters
   */
  async advancedSearch(
    query: string,
    filters: SearchFilters = {},
    page: number = 1,
    limit: number = 20,
  ): Promise<SearchResult> {
    const startTime = Date.now();

    try {
      const videos = await this.searchVideos(query, filters, limit);
      const searchTime = Date.now() - startTime;

      return {
        videos,
        totalResults: videos.length,
        searchTime,
        relatedQueries: [], // TODO: Implement related queries
      };
    } catch (error) {
      console.error('Advanced search failed:', error);
      return {
        videos: [],
        totalResults: 0,
        searchTime: Date.now() - startTime,
        relatedQueries: [],
      };
    }
  }

  /**
   * Search within a specific channel
   */
  async searchInChannel(
    channelId: string,
    query: string,
    filters: Omit<SearchFilters, 'type'> = {},
    limit: number = 20,
  ): Promise<Video[]> {
    try {
      const response = await api.get<Video[]>(`/api/channels/${channelId}/search`, {
        q: query,
        ...filters,
        limit,
      });
      return response.data;
    } catch (error) {
      console.error('Channel search failed:', error);
      return [];
    }
  }

  /**
   * Get autocomplete suggestions
   */
  async getAutocompleteSuggestions(
    query: string,
    limit: number = 8,
  ): Promise<string[]> {
    try {
      const response = await api.get<string[]>('/api/search/autocomplete', {
        q: query,
        limit,
      });
      return response.data;
    } catch (error) {
      console.error('Autocomplete failed:', error);
      return [];
    }
  }

  /**
   * Search by image (reverse image search)
   */
  async searchByImage(
    image: File,
    filters: SearchFilters = {},
  ): Promise<Video[]> {
    try {
      const response = await api.upload<Video[]>('/api/search/image', image, filters);
      return response.data;
    } catch (error) {
      console.error('Image search failed:', error);
      return [];
    }
  }

  /**
   * Get search history
   */
  async getSearchHistory(limit: number = 50): Promise<Array<{
    query: string;
    timestamp: string;
    filters?: SearchFilters;
  }>> {
    try {
      const response = await api.get('/api/search/history', { limit });
      return response.data;
    } catch (error) {
      console.error('Failed to get search history:', error);
      return [];
    }
  }

  /**
   * Clear search history
   */
  async clearSearchHistory(): Promise<void> {
    try {
      await api.delete('/api/search/history');
    } catch (error) {
      console.error('Failed to clear search history:', error);
    }
  }
}

export const searchService = new SearchService();
export default searchService;

