/**
 * Unified Comment Type Definition
 * 
 * This is the single source of truth for comment-related types across the application.
 */

import type { BaseEntity } from './BaseEntity';

export interface CommentAuthor {
  id: string;
  name: string;
  avatar: string;
  avatarUrl?: string; // Legacy alias
  channelUrl?: string;
  channelId?: string;
  isChannelOwner: boolean;
  isVerified: boolean;
  verified?: boolean; // Legacy alias
}

/**
 * Unified Comment Interface
 * 
 * This interface consolidates all comment properties from different features
 * and provides a single source of truth for comment data.
 */
export interface Comment extends BaseEntity {
  // Core content
  content: string;
  commentText?: string; // Legacy alias
  textDisplay?: string; // Legacy alias
  textOriginal?: string; // Legacy alias
  
  // Author information
  author?: CommentAuthor;
  authorId: string;
  authorName: string;
  authorAvatar: string;
  authorAvatarUrl?: string; // Legacy alias
  authorThumbnail?: string; // Legacy alias
  authorChannelUrl?: string;
  authorChannelId?: string;
  
  // User display fields (legacy compatibility)
  userAvatarUrl?: string;
  userName?: string;
  
  // Associated content
  videoId: string;
  
  // Comment hierarchy
  parentId?: string;
  replies: Comment[];
  replyCount: number;
  totalReplyCount?: number; // Legacy alias
  replyTo?: string;
  
  // Engagement
  likes: number;
  likeCount?: number; // Legacy alias
  dislikes: number;
  
  // User interaction states
  isLikedByCurrentUser: boolean;
  isDislikedByCurrentUser: boolean;
  
  // Status flags
  isEdited: boolean;
  isPinned: boolean;
  isHearted: boolean;
  isPublic?: boolean;
  
  // Temporal data
  timestamp: string;
  publishedAt?: string; // Legacy alias
  
  // Permissions and capabilities
  canRate?: boolean;
  canReply?: boolean;
  viewerRating?: 'like' | 'dislike' | 'none';
  
  // YouTube API compatibility
  snippet?: {
    videoId?: string;
    textDisplay?: string;
    textOriginal?: string;
    parentId?: string;
    authorDisplayName?: string;
    authorProfileImageUrl?: string;
    authorChannelUrl?: string;
    authorChannelId?: {
      value: string;
    };
    canRate?: boolean;
    viewerRating?: 'like' | 'dislike' | 'none';
    likeCount?: number;
    publishedAt?: string;
    updatedAt?: string;
  };
}

// Strict comment type for new implementations
export interface StrictComment {
  id: string;
  videoId: string;
  author: CommentAuthor;
  text: string;
  timestamp: string;
  likes: number;
  replies: StrictComment[];
  edited: boolean;
  pinned: boolean;
  heartedByCreator: boolean;
}

export default Comment;
