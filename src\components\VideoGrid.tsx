import { memo } from 'react';

import { VideoCard } from '@/components/VideoCard';
import { cn } from '@/lib/utils';


export interface VideoGridProps {
  videos: any[];
  className?: string;
  loading?: boolean;
  skeletonCount?: number;
  onVideoMoreClick?: (videoId: string) => void;
  title?: string;
  showMoreLink?: string;
  emptyMessage?: string;
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  } | number;
  keyPrefix?: string;
  variant?: 'default' | 'compact' | 'studio';
}

export const VideoGrid = memo(({
  videos,
  className,
  loading = false,
  skeletonCount = 12,
  onVideoMoreClick,
  title,
  showMoreLink,
  emptyMessage = 'No videos available',
  columns = {
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
    '2xl': 6,
  },
  keyPrefix = '',
  variant = 'default',
}: VideoGridProps) => {
  // Get grid classes based on columns configuration
  const getGridClasses = () => {
    if (typeof columns === 'number') {
      // Simple number configuration
      switch (columns) {
        case 1: return 'grid-cols-1';
        case 2: return 'grid-cols-1 sm:grid-cols-2';
        case 3: return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3';
        case 4: return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4';
        case 5: return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5';
        case 6: return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6';
        default: return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
      }
    } else {
      // Object configuration
      const baseClasses = 'grid grid-cols-1';
      const responsiveClasses = [
        columns.sm && `sm:grid-cols-${columns.sm}`,
        columns.md && `md:grid-cols-${columns.md}`,
        columns.lg && `lg:grid-cols-${columns.lg}`,
        columns.xl && `xl:grid-cols-${columns.xl}`,
        columns['2xl'] && `2xl:grid-cols-${columns['2xl']}`,
      ].filter(Boolean).join(' ');

      return `${baseClasses} ${responsiveClasses}`;
    }
  };

  // Loading skeleton component
  const VideoCardSkeleton = () => (
    <div className="flex flex-col space-y-2">
      <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
      <div className="flex space-x-2">
        <div className="w-9 h-9 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full animate-pulse" />
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" />
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse" />
        </div>
      </div>
    </div>
  );

  // Empty state component
  const EmptyState = () => (
    <div className="flex items-center justify-center py-10">
      <div className="text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-16 w-16 mx-auto text-gray-400 mb-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
          />
        </svg>
        <p className="text-gray-600 dark:text-gray-400">{emptyMessage}</p>
      </div>
    </div>
  );

  return (
    <div className="mb-8">
      {/* Title row with optional "Show more" link */}
      {title && (
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">{title}</h2>
          {showMoreLink && (
            <a
              href={showMoreLink}
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
            >
              Show more
            </a>
          )}
        </div>
      )}

      {/* Loading state */}
      {loading && (
        <div
          className={cn(
            'grid gap-4',
            getGridClasses(),
            className,
          )}
        >
          {Array.from({ length: skeletonCount }).map((_, i) => (
            <VideoCardSkeleton key={`skeleton-${i}`} />
          ))}
        </div>
      )}

      {/* Video grid */}
      {!loading && videos.length > 0 && (
        <div
          className={cn(
            'grid gap-4',
            getGridClasses(),
            className,
          )}
        >
          {videos.map((video, index) => (
            <VideoCard
              key={keyPrefix ? `${keyPrefix}-${video.id}` : `${video.id}-${index}`}
              {...video}
              onMoreClick={onVideoMoreClick}
              variant={variant}
            />
          ))}
        </div>
      )}

      {/* Empty state */}
      {!loading && videos.length === 0 && <EmptyState />}
    </div>
  );
});

VideoGrid.displayName = 'VideoGrid';

export default VideoGrid;
