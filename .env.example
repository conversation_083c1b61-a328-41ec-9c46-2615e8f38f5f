# YouTube Studio Clone - Environment Variables
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# API CONFIGURATION
# =============================================================================

# YouTube Data API v3 Key
# Get your API key from: https://console.developers.google.com/
# Enable YouTube Data API v3 for your project
VITE_YOUTUBE_API_KEY=your_youtube_api_key_here

# Google Gemini AI API Key
# Get your API key from: https://makersuite.google.com/app/apikey
# Used for AI-powered content suggestions and analysis
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Google Custom Search API Configuration
# Get your API key from: https://console.developers.google.com/
# Create a Custom Search Engine at: https://cse.google.com/
# Configure your CSE to search YouTube videos specifically
VITE_GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
VITE_GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Name
VITE_APP_NAME="YouTube Studio Clone"

# Application Version
VITE_APP_VERSION="2.0.0"

# Application Environment
VITE_APP_ENV=development

# Base URL for API calls
VITE_API_BASE_URL=https://www.googleapis.com/youtube/v3

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable AI features
VITE_ENABLE_AI_FEATURES=true

# Enable/disable analytics
VITE_ENABLE_ANALYTICS=true

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# API Configuration
VITE_API_TIMEOUT=10000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RETRY_DELAY=1000

# Rate Limiting
VITE_RATE_LIMIT_REQUESTS=100
VITE_RATE_LIMIT_WINDOW=60000

# Caching
VITE_CACHE_TTL=300000
VITE_CACHE_MAX_SIZE=100
VITE_VIDEO_PRELOAD_COUNT=5
VITE_VIDEO_CACHE_SIZE=50

# Pagination
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# Performance Monitoring
VITE_PERFORMANCE_SAMPLE_RATE=0.1

# Performance Budgets (in milliseconds)
VITE_PERFORMANCE_LCP_BUDGET=2500
VITE_PERFORMANCE_FCP_BUDGET=1800
VITE_PERFORMANCE_CLS_BUDGET=0.1
VITE_PERFORMANCE_TTFB_BUDGET=800

# Bundle Size Budgets (in KB)
VITE_BUNDLE_MAIN_BUDGET=500
VITE_BUNDLE_VENDOR_BUDGET=1000
VITE_BUNDLE_CHUNK_BUDGET=200

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================

# Analytics Services
VITE_ANALYTICS_ENABLED=false
VITE_GOOGLE_ANALYTICS_ID=your_ga_id_here
VITE_MIXPANEL_TOKEN=your_mixpanel_token_here

# Tracking Settings
VITE_TRACK_PAGE_VIEWS=true
VITE_TRACK_CLICKS=false
VITE_TRACK_SCROLL_DEPTH=false
VITE_TRACK_PERFORMANCE=false
VITE_TRACK_ERRORS=true

# Analytics Data Management
VITE_ANALYTICS_LOCAL_TTL=604800000
VITE_ANALYTICS_BATCH_SIZE=10
VITE_ANALYTICS_FLUSH_INTERVAL=30000

# =============================================================================
# ERROR HANDLING CONFIGURATION
# =============================================================================

# Error Tracking
VITE_ERROR_TRACKING_ENABLED=true
VITE_SENTRY_DSN=your_sentry_dsn_here
VITE_REPORT_ERRORS=false
VITE_ERROR_ENDPOINT=your_error_endpoint_here
VITE_MAX_ERRORS_PER_SESSION=50
VITE_ERROR_THROTTLE_MS=1000

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# UI Features
VITE_FEATURE_DARK_MODE=true
VITE_FEATURE_INFINITE_SCROLL=true
VITE_FEATURE_VIDEO_AUTOPLAY=false
VITE_FEATURE_LAZY_LOADING=true

# Advanced Features
VITE_FEATURE_OFFLINE_MODE=false
VITE_FEATURE_PWA=false
VITE_FEATURE_PUSH_NOTIFICATIONS=false

# Experimental Features
VITE_FEATURE_AI_RECOMMENDATIONS=false
VITE_FEATURE_VOICE_SEARCH=false
VITE_FEATURE_GESTURE_CONTROLS=false

# Development Features
VITE_DEBUG_MODE=false
VITE_MOCK_API=false
VITE_PERFORMANCE_OVERLAY=false

# =============================================================================
# UI CONFIGURATION
# =============================================================================

# Theme
VITE_DEFAULT_THEME=light

# Layout
VITE_SIDEBAR_WIDTH=280
VITE_HEADER_HEIGHT=64
VITE_FOOTER_HEIGHT=80

# Video Player
VITE_DEFAULT_VOLUME=0.8
VITE_DEFAULT_QUALITY=auto
VITE_AUTOPLAY_DELAY=3000

# Animations
VITE_ANIMATION_DURATION=300
VITE_REDUCED_MOTION=false

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# Storage Limits
VITE_MAX_HISTORY_ITEMS=1000
VITE_MAX_SEARCH_HISTORY=100
VITE_MAX_CACHE_SIZE_MB=50

# Cleanup Settings
VITE_STORAGE_CLEANUP_INTERVAL=86400000
VITE_CACHE_CLEANUP_THRESHOLD=0.8

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Content Security Policy
VITE_CSP_ENABLED=false

# API Security
VITE_API_KEY_ROTATION_INTERVAL=86400000

# Input Validation
VITE_MAX_INPUT_LENGTH=1000
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp,mp4,webm
VITE_MAX_FILE_SIZE_MB=10

# Rate Limiting
VITE_SEARCH_RATE_LIMIT=10
VITE_COMMENT_RATE_LIMIT=5
VITE_LIKE_RATE_LIMIT=30

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Logging
VITE_LOG_LEVEL=debug
VITE_CONSOLE_LOGS=true

# Testing
VITE_MOCK_DELAY=500
VITE_MOCK_ERROR_RATE=0.1

# Enable/disable live streaming features
VITE_ENABLE_LIVE_STREAMING=true

# Enable/disable performance monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Enable/disable mock data (for development)
VITE_USE_MOCK_DATA=false

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# API cache duration in milliseconds (15 minutes)
VITE_API_CACHE_DURATION=900000

# Maximum number of videos to load per page
VITE_MAX_VIDEOS_PER_PAGE=50

# Virtual list item height in pixels
VITE_VIRTUAL_LIST_ITEM_HEIGHT=200

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development server port
VITE_DEV_PORT=5173

# Enable/disable hot module replacement
VITE_HMR=true

# Enable/disable source maps in development
VITE_SOURCE_MAPS=true

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================

# Google Analytics Measurement ID (optional)
# VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Sentry DSN for error tracking (optional)
# VITE_SENTRY_DSN=https://your-sentry-dsn

# =============================================================================
# SOCIAL MEDIA CONFIGURATION
# =============================================================================

# Social media sharing URLs
VITE_TWITTER_SHARE_URL=https://twitter.com/intent/tweet
VITE_FACEBOOK_SHARE_URL=https://www.facebook.com/sharer/sharer.php
VITE_LINKEDIN_SHARE_URL=https://www.linkedin.com/sharing/share-offsite/

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Content Security Policy (CSP) settings
VITE_CSP_ENABLED=true

# CORS allowed origins (comma-separated)
VITE_CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# Local storage key prefix
VITE_STORAGE_PREFIX=yt_studio_

# Session storage enabled
VITE_SESSION_STORAGE_ENABLED=true

# =============================================================================
# UI CONFIGURATION
# =============================================================================

# Default theme (light, dark, system)
VITE_DEFAULT_THEME=system

# Enable/disable animations
VITE_ENABLE_ANIMATIONS=true

# Default language
VITE_DEFAULT_LANGUAGE=en

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Enable/disable test mode
VITE_TEST_MODE=false

# Test API delay in milliseconds
VITE_TEST_API_DELAY=1000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (error, warn, info, debug)
VITE_LOG_LEVEL=info

# Enable/disable console logging
VITE_CONSOLE_LOGGING=true

# Enable/disable file logging
VITE_FILE_LOGGING=false

# =============================================================================
# NOTES
# =============================================================================

# 1. All VITE_ prefixed variables are exposed to the client-side code
# 2. Never commit actual API keys to version control
# 3. Use different API keys for development and production
# 4. Some features may require additional API keys or services
# 5. Check the documentation for the latest configuration options