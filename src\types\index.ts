/**
 * Centralized Type Definitions Index
 * 
 * This is the single source of truth for all type exports in the application.
 * All global interfaces are now centralized and properly organized.
 */

// === Core Entity Types ===
export type { BaseEntity } from './BaseEntity';
export type { User, UserRole, UserPreferences, SocialLinks, NotificationSettings, PrivacySettings } from './User';
export type { AuthState, LoginCredentials, RegisterData, AuthTokens, AuthContextType } from './User';
export type { Channel, VideoChannel, ChannelSection, ChannelBranding, ChannelStats } from './Channel';
export type { PlaylistSummary, CommunityPost } from './Channel';
export type { Comment, CommentAuthor, StrictComment } from './Comment';
export type { Playlist, PlaylistVideo, PlaylistSettings, PlaylistVisibility } from './Playlist';
export type { UserPlaylist, UserPlaylistDetails, StrictPlaylist } from './Playlist';

// === Video Types (Primary) ===
export type { Video, ApiVideo, ApiYouTubeVideo, ApiExternalVideo } from './video';
export type { AnalyticsSlice } from './video';
export type {
  VideoChannel as VideoChannelEmbedded,
  VideoStatistics,
  VideoContentDetails,
  VideoTopicDetails,
  VideoStatus,
  VideoLiveStreamingDetails,
  VideoCaption,
  VideoSubtitle,
  VideoQuality,
  VideoChapter,
  VideoMetadata,
  VideoVisibility,
} from './video';

// === Legacy Compatibility Types ===
// These interfaces are kept for backward compatibility
export interface VideoItem {
  id: string;
  title: string;
  channelName: string;
  channelId: string;
  thumbnailUrl: string;
  viewCount: number;
  publishedAt: Date;
  duration: number;
  avatarUrl?: string;
}

export interface VideoCardProps extends VideoItem {
  showChannelInfo?: boolean;
  className?: string;
  onMoreClick: (videoId: string) => void;
}

export interface VideoGridProps {
  videos: VideoItem[];
  className?: string;
  loading?: boolean;
  skeletonCount?: number;
  onVideoMoreClick: (videoId: string) => void;
}

// === Re-export Enhanced Types ===
// Export core types (primary definitions)
export * from './core';
export * from './video';

// Export specific types from other files to avoid conflicts
export type {
  VideoMetadata as StrictVideoMetadata,
  VideoStats as StrictVideoStats,
  VideoFile as StrictVideoFile,
  ChannelBranding as StrictChannelBranding,
  AnalyticsEvent as StrictAnalyticsEvent,
  DeepPartial as StrictDeepPartial,
  RequiredFields as StrictRequiredFields
} from './strictTypes';

// Export specific types from legacy to avoid conflicts
export type {
  ExtendedVideo,
  VideoVisibility as LegacyVideoVisibility,
  UploadProgress as LegacyUploadProgress
} from './legacy';

// Export specific types from unified types to avoid conflicts
export type {
  User as UnifiedUser,
  VideoAnalytics as UnifiedVideoAnalytics,
  EngagementMetrics as UnifiedEngagementMetrics,
  DemographicData as UnifiedDemographicData,
  MonetizationSettings as UnifiedMonetizationSettings,
  SearchFilters as UnifiedSearchFilters,
  SearchResult as UnifiedSearchResult,
  Subscription as UnifiedSubscription,
  TrafficSource as UnifiedTrafficSource,
  VideoEvent as UnifiedVideoEvent,
  Chapter as UnifiedChapter
} from './unifiedTypes';

// === API Response Types ===
export interface ApiResponse<T> {
  data: T;
  success?: boolean;
  error?: string;
  loading?: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  nextPageToken?: string;
  totalResults: number;
  resultsPerPage: number;
  hasNextPage?: boolean;
  hasPreviousPage?: boolean;
}

// === Missing Type Exports ===
export interface VideoUploadData {
  title: string;
  description: string;
  tags: string[];
  category: string;
  visibility: 'public' | 'unlisted' | 'private';
  thumbnail?: File;
  scheduledPublishTime?: string;
}

export interface UploadProgress {
  percentage: number;
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  message: string;
}

export interface VideoIdeaResponse {
  ideas: Array<{
    title: string;
    description: string;
    tags: string[];
    category: string;
  }>;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// === Utility and UI Types ===
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  lastUpdated?: string;
}

export interface ModalState {
  isOpen: boolean;
  type?: string;
  data?: unknown;
}

// === App Error Types ===
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

// === Form Types ===
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'file';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: string }>;
  validation?: ValidationRule[];
}

export interface ValidationRule {
  type: 'required' | 'email' | 'minLength' | 'maxLength' | 'pattern';
  value?: unknown;
  message: string;
}

// === Theme and UI Types ===
export type VideoCardVariant = 'default' | 'compact' | 'list' | 'grid';
export type VideoCardSize = 'sm' | 'md' | 'lg';
export type ButtonVariant = 'primary' | 'secondary' | 'ghost' | 'danger';
export type ButtonSize = 'sm' | 'md' | 'lg';
export type ThemeMode = 'light' | 'dark' | 'system';

// === Re-export video normalization utilities ===
export * from '../utils/normalizeVideo';
