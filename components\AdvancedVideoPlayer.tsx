import React, { forwardRef } from 'react';

export interface AdvancedVideoPlayerProps {
  src?: string;
  poster?: string;
  title?: string;
  autoPlay?: boolean;
  controls?: boolean;
  muted?: boolean;
  loop?: boolean;
  className?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  onLoadStart?: () => void;
  onError?: (error: Error) => void;
}

const AdvancedVideoPlayer = forwardRef<HTMLVideoElement, AdvancedVideoPlayerProps>(
  ({
    src,
    poster,
    title,
    autoPlay = false,
    controls = true,
    muted = false,
    loop = false,
    className = '',
    onPlay,
    onPause,
    onEnded,
    onLoadStart,
    onError,
    ...props
  }, ref) => {
    const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
      const error = new Error('Video failed to load');
      onError?.(error);
    };

    if (!src) {
      return (
        <div className={`flex items-center justify-center bg-gray-900 text-white aspect-video ${className}`}>
          <div className="text-center">
            <svg className="w-16 h-16 mx-auto mb-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm3 2h6a1 1 0 011 1v8a1 1 0 01-1 1H7a1 1 0 01-1-1V6a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            <p className="text-gray-400">No video source provided</p>
          </div>
        </div>
      );
    }

    return (
      <video
        ref={ref}
        src={src}
        poster={poster}
        title={title}
        autoPlay={autoPlay}
        controls={controls}
        muted={muted}
        loop={loop}
        className={`w-full aspect-video bg-black ${className}`}
        onPlay={onPlay}
        onPause={onPause}
        onEnded={onEnded}
        onLoadStart={onLoadStart}
        onError={handleError}
        {...props}
      >
        <p className="text-white p-4">
          Your browser does not support the video tag.
        </p>
      </video>
    );
  }
);

AdvancedVideoPlayer.displayName = 'AdvancedVideoPlayer';

export default AdvancedVideoPlayer;
