{"timestamp": "2025-07-01T08:30:22Z", "baseline_errors": {"typescript_errors": 196, "eslint_errors": 647, "total_errors": 843}, "duplicate_components": {"VideoCard": ["components/VideoCard.tsx", "src/components/molecules/VideoCard/VideoCard.tsx", "src/features/video/components/VideoCard.tsx"], "VideoGrid": ["components/VideoGrid.tsx", "src/components/organisms/VideoGrid/VideoGrid.tsx", "src/features/video/components/VideoGrid.tsx"], "SearchBar": ["components/SearchBar.tsx"], "UnifiedVideoCard": ["components/UnifiedVideoCard.tsx", "src/components/unified/UnifiedVideoCard.tsx"], "YouTubePlayer": ["components/YouTubePlayer.tsx", "components/YouTubePlayerWrapper.tsx"], "Button": ["components/ui/Button.tsx", "components/forms/Button.tsx", "src/components/atoms/Button/Button.tsx"], "LoadingSpinner": ["components/LoadingSpinner.tsx", "components/ui/LoadingSpinner.tsx"]}, "overlapping_types": {"Video": ["types.ts", "src/types/core.ts", "src/types/video.ts", "types/unifiedTypes.ts"], "UnifiedVideoMetadata": ["src/services/metadataNormalizationService.ts", "src/services/unifiedDataService.ts"], "YouTubeSearchResult": ["services/googleSearchService.ts"], "GoogleSearchResult": ["services/googleSearchService.ts"]}, "legacy_vs_src_structure": {"root_level_components": {"count": 85, "examples": ["components/VideoCard.tsx", "components/VideoGrid.tsx", "components/SearchBar.tsx", "components/Header.tsx"]}, "src_components": {"count": 45, "examples": ["src/components/molecules/VideoCard/VideoCard.tsx", "src/components/organisms/VideoGrid/VideoGrid.tsx", "src/features/video/components/VideoCard.tsx"]}}, "major_issues_identified": ["Type conflicts between Video interfaces", "Duplicate component implementations", "Missing type imports", "Inconsistent exactOptionalPropertyTypes usage", "Mixed component architecture (root vs src/)", "Unused imports and variables", "ESLint formatting issues"], "refactor_priorities": ["1. Fix TypeScript type conflicts", "2. Consolidate duplicate components", "3. Standardize type definitions", "4. Fix ESLint formatting issues", "5. Remove unused code", "6. Establish consistent architecture"]}