
import type * as React from 'react';
import {  useState  } from 'react';

import { FireIcon } from '@heroicons/react/24/solid';

import CategoryTabs from '@/features/video/components/CategoryTabs';
import PageLayout from '@/features/video/components/PageLayout';
import { useTrendingVideos } from '@/features/video/hooks';
import { VideoCard } from '@/features/video/src/components';

const TrendingPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<'all' | 'music' | 'gaming' | 'news' | 'movies'>('all');
  const { data: trendingVideos, loading, error } = useTrendingVideos(activeCategory);

  const categories = [
    { id: 'all' as const, label: 'All', icon: '🔥' },
    { id: 'music' as const, label: 'Music', icon: '🎵' },
    { id: 'gaming' as const, label: 'Gaming', icon: '🎮' },
    { id: 'news' as const, label: 'News', icon: '📰' },
    { id: 'movies' as const, label: 'Movies', icon: '🎬' },
  ];

  return (
    <PageLayout
      title="Trending"
      icon={<FireIcon className="w-8 h-8 text-red-500" />}
      data={trendingVideos}
      loading={loading}
      error={error}
      emptyState={{
        title: 'No trending videos found',
        message: 'Check back later for the latest trending content.',
      }}
      headerActions={<CategoryTabs categories={categories} activeCategory={activeCategory} setActiveCategory={setActiveCategory} />}
    >
      {(videos) => (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-x-3 md:gap-x-4 gap-y-5 md:gap-y-6">
          {(videos || []).map(video => (
            <VideoCard key={video.id} {...video} onMoreClick={() => {}} />
          ))}
        </div>
      )}
    </PageLayout>
  );
};

export default TrendingPage;