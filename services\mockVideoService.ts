import { Video } from '../src/types';

// Mock video data for development
const mockVideos: Video[] = [
  {
    id: 'mock-1',
    title: 'Sample Short Video 1',
    description: 'This is a sample short video for testing',
    thumbnailUrl: 'https://via.placeholder.com/300x400?text=Short+1',
    videoUrl: '/videos/sample-short-1.mp4',
    duration: 30,
    views: '1.2K',
    viewCount: 1200,
    likes: 150,
    likeCount: 150,
    dislikes: 5,
    dislikeCount: 5,
    commentCount: 25,
    channelId: 'channel-1',
    channelName: 'Content Creator 1',
    channelAvatarUrl: 'https://via.placeholder.com/50x50?text=CC1',
    publishedAt: '2024-01-15T10:00:00Z',
    uploadedAt: '2 hours ago',
    category: 'Entertainment',
    tags: ['funny', 'viral', 'trending'],
    language: 'en',
    isShort: true,
    visibility: 'public' as const,
    status: 'published' as const,
    source: 'local' as const,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'mock-2',
    title: 'Sample Short Video 2',
    description: 'Another sample short video',
    thumbnailUrl: 'https://via.placeholder.com/300x400?text=Short+2',
    videoUrl: '/videos/sample-short-2.mp4',
    duration: 45,
    views: '856',
    viewCount: 856,
    likes: 95,
    likeCount: 95,
    dislikes: 2,
    dislikeCount: 2,
    commentCount: 12,
    channelId: 'channel-2',
    channelName: 'Creator Studio',
    channelAvatarUrl: 'https://via.placeholder.com/50x50?text=CS',
    publishedAt: '2024-01-14T15:30:00Z',
    uploadedAt: '1 day ago',
    category: 'Music',
    tags: ['music', 'cover', 'original'],
    language: 'en',
    isShort: true,
    visibility: 'public' as const,
    status: 'published' as const,
    source: 'local' as const,
    createdAt: '2024-01-14T15:30:00Z',
    updatedAt: '2024-01-14T15:30:00Z'
  }
];

export async function getShortsVideos(): Promise<Video[]> {
  // Simulate API delay
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockVideos.filter(video => video.isShort));
    }, 100);
  });
}

export async function getVideos(): Promise<Video[]> {
  // Simulate API delay
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockVideos);
    }, 100);
  });
}

export async function getVideoById(id: string): Promise<Video | null> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const video = mockVideos.find(v => v.id === id);
      resolve(video || null);
    }, 100);
  });
}
