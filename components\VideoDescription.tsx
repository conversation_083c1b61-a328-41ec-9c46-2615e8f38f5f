import React, { useState } from 'react';
import { Video } from '../src/types';

export interface VideoDescriptionProps {
  video: Video;
  className?: string;
}

export default function VideoDescription({ video, className = '' }: VideoDescriptionProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);

  const description = video.description || '';
  const maxLength = 200;
  const shouldTruncate = description.length > maxLength;
  const displayText = shouldTruncate && !showFullDescription 
    ? description.slice(0, maxLength) + '...'
    : description;

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const formatNumber = (num?: number | string) => {
    if (!num) return '0';
    const number = typeof num === 'string' ? parseInt(num.replace(/[^\d]/g, ''), 10) : num;
    if (isNaN(number)) return '0';
    
    if (number < 1000) return number.toString();
    if (number < 1000000) return `${(number / 1000).toFixed(1)}K`;
    return `${(number / 1000000).toFixed(1)}M`;
  };

  return (
    <div className={`bg-gray-100 rounded-lg p-4 ${className}`}>
      {/* Video Stats */}
      <div className="flex items-center space-x-4 text-sm font-medium mb-3">
        <span>{formatNumber(video.viewCount || video.views)} views</span>
        <span>•</span>
        <span>{formatDate(video.publishedAt || video.uploadedAt)}</span>
        <span>•</span>
        <span>{formatNumber(video.likeCount || video.likes)} likes</span>
      </div>

      {/* Description */}
      <div className="space-y-3">
        <div className="whitespace-pre-wrap text-sm leading-relaxed">
          {displayText}
        </div>

        {shouldTruncate && (
          <button
            onClick={() => setShowFullDescription(!showFullDescription)}
            className="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors"
          >
            {showFullDescription ? 'Show less' : 'Show more'}
          </button>
        )}

        {/* Tags */}
        {video.tags && video.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {video.tags.slice(0, 5).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full hover:bg-blue-200 cursor-pointer transition-colors"
              >
                #{tag}
              </span>
            ))}
            {video.tags.length > 5 && (
              <span className="text-xs text-gray-500 px-2 py-1">
                +{video.tags.length - 5} more
              </span>
            )}
          </div>
        )}

        {/* Additional Info */}
        <div className="border-t pt-3 mt-4">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center justify-between w-full text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
          >
            <span>Video details</span>
            <svg
              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isExpanded && (
            <div className="mt-3 space-y-2 text-sm text-gray-600">
              {video.category && (
                <div>
                  <span className="font-medium">Category:</span> {video.category}
                </div>
              )}
              {video.language && (
                <div>
                  <span className="font-medium">Language:</span> {video.language}
                </div>
              )}
              {video.duration && (
                <div>
                  <span className="font-medium">Duration:</span>{' '}
                  {typeof video.duration === 'number'
                    ? `${Math.floor(video.duration / 60)}:${(video.duration % 60).toString().padStart(2, '0')}`
                    : video.duration}
                </div>
              )}
              {video.visibility && (
                <div>
                  <span className="font-medium">Visibility:</span> {video.visibility}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
