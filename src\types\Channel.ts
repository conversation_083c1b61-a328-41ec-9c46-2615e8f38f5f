/**
 * Unified Channel Type Definition
 * 
 * This is the single source of truth for channel-related types across the application.
 */

import type { BaseEntity } from './BaseEntity';
import type { SocialLinks } from './User';

export interface PlaylistSummary {
  id: string;
  title: string;
  videoCount: number;
  thumbnailUrl?: string;
}

export interface CommunityPost {
  id: string;
  type: 'text' | 'image' | 'poll';
  content: string;
  imageUrl?: string;
  pollOptions?: Array<{ id: string; text: string; votes: number }>;
  likes: number;
  comments: number;
  shares: number;
  isLiked: boolean;
  createdAt: Date;
  engagement: {
    views: number;
    clickThroughRate: number;
  };
  // Legacy fields for backward compatibility
  channelName?: string;
  channelAvatarUrl?: string;
  timestamp?: string;
  textContent?: string;
  commentsCount?: number;
}

export interface ChannelSection {
  id: string;
  title: string;
  type: 'videos' | 'playlists' | 'channels';
  items: string[]; // IDs of videos, playlists, or channels
  order: number;
}

export interface ChannelBranding {
  bannerUrl: string;
  logoUrl: string;
  watermarkUrl: string;
  primaryColor: string;
  secondaryColor: string;
}

export interface ChannelStats {
  subscribers: number;
  totalViews: number;
  totalVideos: number;
  averageViews: number;
  engagementRate: number;
}

/**
 * Unified Channel Interface
 * 
 * This interface consolidates all channel properties from different features
 * and provides a single source of truth for channel data.
 */
export interface Channel extends BaseEntity {
  // Core identification
  name: string;
  handle?: string;
  description: string;
  
  // Visual assets
  avatarUrl: string;
  banner?: string;
  branding?: ChannelBranding;
  
  // Statistics
  subscribers: number;
  subscriberCount: string; // For display purposes (e.g., "1.2M")
  videoCount: number;
  totalViews?: number;
  stats?: ChannelStats;
  
  // Status and verification
  isVerified: boolean;
  verified?: boolean; // Legacy alias
  monetized?: boolean;
  
  // Profile information
  joinedDate?: string;
  joinDate?: string; // Legacy alias
  country?: string;
  language?: string;
  categories?: string[];
  
  // Content organization
  links?: SocialLinks;
  playlists?: PlaylistSummary[];
  sections?: ChannelSection[];
  communityPosts?: CommunityPost[];
  
  // Legacy compatibility fields
  channelDescription?: string; // Alias for description
}

// Simple Channel interface for embedded use
export interface VideoChannel {
  id: string;
  name: string;
  avatarUrl?: string;
  subscribers?: number;
  subscriberCount?: string;
  isVerified?: boolean;
  handle?: string;
}

export default Channel;
