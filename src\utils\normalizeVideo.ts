/**
 * Video Normalization Utilities
 *
 * Adapter functions to map external API responses to the internal unified Video interface.
 * This ensures consistent data structures throughout the application regardless of the data source.
 */

import type { Video, ApiYouTubeVideo, ApiExternalVideo, ApiVideo } from '@/types/video';

/**
 * Utility functions for data transformation
 */
const formatUtils = {
  /**
   * Parse ISO 8601 duration (PT4M13S) to readable format (4:13)
   */
  parseDuration(isoDuration: string): string {
    if (!isoDuration) {
return '0:00';
}

    const regex = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/;
    const matches = isoDuration.match(regex);

    if (!matches) {
return '0:00';
}

    const hours = parseInt(matches[1] || '0', 10);
    const minutes = parseInt(matches[2] || '0', 10);
    const seconds = parseInt(matches[3] || '0', 10);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  },

  /**
   * Format view count to human readable string
   */
  formatViewCount(count: number): string {
    if (count < 1000) {
return `${count} views`;
}
    if (count < 1000000) {
return `${(count / 1000).toFixed(1)}K views`;
}
    if (count < 1000000000) {
return `${(count / 1000000).toFixed(1)}M views`;
}
    return `${(count / 1000000000).toFixed(1)}B views`;
  },

  /**
   * Get the best available thumbnail URL
   */
  getBestThumbnail(thumbnails: Record<string, { url: string; width?: number; height?: number }> | undefined): string {
    if (!thumbnails) {
return '';
}

    // Prefer high resolution thumbnails
    if (thumbnails.maxres?.url) {
return thumbnails.maxres.url;
}
    if (thumbnails.standard?.url) {
return thumbnails.standard.url;
}
    if (thumbnails.high?.url) {
return thumbnails.high.url;
}
    if (thumbnails.medium?.url) {
return thumbnails.medium.url;
}
    if (thumbnails.default?.url) {
return thumbnails.default.url;
}

    return '';
  },

  /**
   * Map YouTube privacy status to our VideoVisibility type
   */
  mapPrivacyStatus(privacyStatus?: string): 'public' | 'unlisted' | 'private' | 'scheduled' {
    switch (privacyStatus) {
      case 'public': return 'public';
      case 'unlisted': return 'unlisted';
      case 'private': return 'private';
      case 'scheduled': return 'scheduled';
      default: return 'public';
    }
  },

  /**
   * Determine if a video is a short based on duration
   */
  isShortVideo(duration: string): boolean {
    if (!duration) {
return false;
}

    const regex = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/;
    const matches = duration.match(regex);

    if (!matches) {
return false;
}

    const hours = parseInt(matches[1] || '0', 10);
    const minutes = parseInt(matches[2] || '0', 10);
    const seconds = parseInt(matches[3] || '0', 10);

    const totalSeconds = hours * 3600 + minutes * 60 + seconds;
    return totalSeconds <= 60; // 60 seconds or less is considered a short
  },

  /**
   * Get category name from category ID (YouTube specific)
   */
  getCategoryName(categoryId: string): string {
    const categories: Record<string, string> = {
      '1': 'Film & Animation',
      '2': 'Autos & Vehicles',
      '10': 'Music',
      '15': 'Pets & Animals',
      '17': 'Sports',
      '18': 'Short Movies',
      '19': 'Travel & Events',
      '20': 'Gaming',
      '21': 'Videoblogging',
      '22': 'People & Blogs',
      '23': 'Comedy',
      '24': 'Entertainment',
      '25': 'News & Politics',
      '26': 'Howto & Style',
      '27': 'Education',
      '28': 'Science & Technology',
      '29': 'Nonprofits & Activism',
      '30': 'Movies',
      '31': 'Anime/Animation',
      '32': 'Action/Adventure',
      '33': 'Classics',
      '34': 'Comedy',
      '35': 'Documentary',
      '36': 'Drama',
      '37': 'Family',
      '38': 'Foreign',
      '39': 'Horror',
      '40': 'Sci-Fi/Fantasy',
      '41': 'Thriller',
      '42': 'Shorts',
      '43': 'Shows',
      '44': 'Trailers',
    };

    return categories[categoryId] || 'Entertainment';
  },
};

/**
 * Normalize YouTube API video response to unified Video interface
 */
export function normalizeYouTubeVideo(apiVideo: ApiYouTubeVideo): Video {
  const { snippet, statistics, contentDetails, status, liveStreamingDetails, topicDetails } = apiVideo;

  const viewCount = parseInt(statistics.viewCount || '0', 10);
  const likeCount = parseInt(statistics.likeCount || '0', 10);
  const dislikeCount = parseInt(statistics.dislikeCount || '0', 10);
  const commentCount = parseInt(statistics.commentCount || '0', 10);

  const now = new Date().toISOString();

  return {
    // BaseEntity fields
    id: apiVideo.id,
    createdAt: snippet.publishedAt,
    updatedAt: now,

    // Core identification
    title: snippet.title,
    description: snippet.description,

    // Media assets
    thumbnailUrl: formatUtils.getBestThumbnail(snippet.thumbnails),
    videoUrl: `https://www.youtube.com/watch?v=${apiVideo.id}`,

    // Duration
    duration: formatUtils.parseDuration(contentDetails.duration),

    // View metrics
    views: formatUtils.formatViewCount(viewCount),
    viewCount,

    // Engagement metrics
    likes: likeCount,
    likeCount,
    dislikes: dislikeCount,
    dislikeCount,
    commentCount,

    // Temporal data
    publishedAt: snippet.publishedAt,
    uploadedAt: snippet.publishedAt,

    // Channel information
    channelId: snippet.channelId,
    channelName: snippet.channelTitle,
    channelAvatarUrl: '', // Will need to be fetched separately
    channelTitle: snippet.channelTitle,

    // Content metadata
    category: formatUtils.getCategoryName(snippet.categoryId),
    tags: snippet.tags || [],

    // Content type flags
    isLive: snippet.liveBroadcastContent === 'live',
    isShort: formatUtils.isShortVideo(contentDetails.duration),
    isUpcoming: snippet.liveBroadcastContent === 'upcoming',

    // Visibility and privacy
    visibility: formatUtils.mapPrivacyStatus(status?.privacyStatus),
    privacyStatus: status?.privacyStatus as 'public' | 'private' | 'unlisted' | 'scheduled',

    // Content safety and restrictions
    isFamilySafe: !status?.madeForKids && !status?.selfDeclaredMadeForKids,
    allowedRegions: contentDetails.regionRestriction?.allowed,
    blockedRegions: contentDetails.regionRestriction?.blocked,
    isAgeRestricted: false, // Would need additional logic to determine
    embeddable: status?.embeddable ?? true,

    // Live streaming specific
    scheduledStartTime: liveStreamingDetails?.scheduledStartTime,
    concurrentViewers: liveStreamingDetails?.concurrentViewers ?
      parseInt(liveStreamingDetails.concurrentViewers, 10) : undefined,
    isLiveContent: !!liveStreamingDetails,
    viewerCount: liveStreamingDetails?.concurrentViewers,

    // Technical details
    uploadStatus: status?.uploadStatus as 'deleted' | 'failed' | 'processed' | 'rejected' | 'uploaded',
    defaultLanguage: snippet.defaultLanguage,
    definition: contentDetails.definition,
    license: status?.license,
    publishAt: status?.publishAt,
    selfDeclaredMadeForKids: status?.selfDeclaredMadeForKids,

    // Structured data
    statistics: {
      viewCount,
      likeCount,
      dislikeCount,
      favoriteCount: parseInt(statistics.favoriteCount || '0', 10),
      commentCount,
    },
    contentDetails: {
      duration: contentDetails.duration,
      dimension: contentDetails.dimension,
      definition: contentDetails.definition,
      caption: contentDetails.caption,
      licensedContent: contentDetails.licensedContent || false,
      regionRestriction: contentDetails.regionRestriction,
      contentRating: contentDetails.contentRating,
      projection: contentDetails.projection,
    },
    topicDetails: topicDetails ? {
      topicIds: topicDetails.topicIds || [],
      relevantTopicIds: topicDetails.relevantTopicIds || [],
      topicCategories: topicDetails.topicCategories || [],
    } : undefined,
    status: status ? {
      uploadStatus: status.uploadStatus,
      failureReason: status.failureReason,
      rejectionReason: status.rejectionReason,
      privacyStatus: status.privacyStatus,
      publishAt: status.publishAt,
      license: status.license,
      embeddable: status.embeddable,
      publicStatsViewable: status.publicStatsViewable,
      madeForKids: status.madeForKids,
      selfDeclaredMadeForKids: status.selfDeclaredMadeForKids,
    } : undefined,
    liveStreamingDetails,

    // Legacy compatibility
    thumbnail: formatUtils.getBestThumbnail(snippet.thumbnails),
    channelAvatar: '', // Will need to be fetched separately

    // Shorts-specific (if applicable)
    isVertical: formatUtils.isShortVideo(contentDetails.duration),
  };
}

/**
 * Normalize external API video response to unified Video interface
 */
export function normalizeExternalVideo(apiVideo: ApiExternalVideo): Video {
  const viewCount = typeof apiVideo.view_count === 'string' ?
    parseInt(apiVideo.view_count, 10) : (apiVideo.view_count || 0);

  const now = new Date().toISOString();
  const createdAt = apiVideo.created_at || now;
  const updatedAt = apiVideo.updated_at || now;

  return {
    // BaseEntity fields
    id: apiVideo.id,
    createdAt,
    updatedAt,

    // Core identification
    title: apiVideo.title,
    description: apiVideo.description || '',

    // Media assets
    thumbnailUrl: apiVideo.thumbnail_url || '',
    videoUrl: apiVideo.url || '',

    // Duration
    duration: typeof apiVideo.duration === 'number' ?
      `${Math.floor(apiVideo.duration / 60)}:${(apiVideo.duration % 60).toString().padStart(2, '0')}` :
      (apiVideo.duration || '0:00'),

    // View metrics
    views: formatUtils.formatViewCount(viewCount),
    viewCount,

    // Engagement metrics
    likes: apiVideo.like_count || 0,
    likeCount: apiVideo.like_count || 0,
    dislikes: 0, // Not typically available in external APIs
    dislikeCount: 0,
    commentCount: 0, // Would need to be fetched separately

    // Temporal data
    publishedAt: createdAt,
    uploadedAt: createdAt,

    // Channel information
    channelId: apiVideo.channel?.id || '',
    channelName: apiVideo.channel?.name || '',
    channelAvatarUrl: apiVideo.channel?.avatar_url || '',
    channelTitle: apiVideo.channel?.name || '',

    // Content metadata
    category: 'Entertainment', // Default category
    tags: [],

    // Content type flags
    isLive: false,
    isShort: false, // Would need duration analysis
    isUpcoming: false,

    // Visibility and privacy
    visibility: 'public', // Default to public
    privacyStatus: 'public',

    // Content safety and restrictions
    isFamilySafe: true, // Default to safe
    isAgeRestricted: false,
    embeddable: true,

    // Legacy compatibility
    thumbnail: apiVideo.thumbnail_url || '',
    channelAvatar: apiVideo.channel?.avatar_url || '',
  };
}

/**
 * Generic normalizer that handles any external API video response
 */
export function normalizeVideo(apiVideo: ApiVideo): Video {
  // Check if it's a YouTube API response
  if ('snippet' in apiVideo && 'statistics' in apiVideo) {
    return normalizeYouTubeVideo(apiVideo);
  }

  // Otherwise, treat as external API response
  return normalizeExternalVideo(apiVideo);
}

/**
 * Normalize an array of video responses
 */
export function normalizeVideos(apiVideos: ApiVideo[]): Video[] {
  return apiVideos.map(normalizeVideo);
}

/**
 * Type guard to check if a video is from YouTube API
 */
export function isYouTubeVideo(video: ApiVideo): video is ApiYouTubeVideo {
  return 'snippet' in video && 'statistics' in video;
}

/**
 * Type guard to check if a video is from external API
 */
export function isExternalVideo(video: ApiVideo): video is ApiExternalVideo {
  return !('snippet' in video) && 'title' in video;
}

export default {
  normalizeVideo,
  normalizeVideos,
  normalizeYouTubeVideo,
  normalizeExternalVideo,
  isYouTubeVideo,
  isExternalVideo,
  formatUtils,
};
