import React from 'react';

interface LikedVideosPageSkeletonProps {
  className?: string;
}

export const LikedVideosPageSkeleton: React.FC<LikedVideosPageSkeletonProps> = ({ 
  className = '' 
}) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {/* Header skeleton */}
      <div className="mb-6">
        <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
        <div className="h-4 bg-gray-300 rounded w-1/2"></div>
      </div>

      {/* Sort controls skeleton */}
      <div className="flex justify-between items-center mb-6">
        <div className="h-4 bg-gray-300 rounded w-24"></div>
        <div className="h-10 bg-gray-300 rounded w-32"></div>
      </div>

      {/* Video grid skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {Array.from({ length: 12 }).map((_, index) => (
          <div key={index} className="space-y-3">
            <div className="aspect-video bg-gray-300 rounded"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-full"></div>
              <div className="h-3 bg-gray-300 rounded w-3/4"></div>
              <div className="h-3 bg-gray-300 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LikedVideosPageSkeleton;
