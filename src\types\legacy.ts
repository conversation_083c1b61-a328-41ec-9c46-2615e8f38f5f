
// Import and re-export VideoVisibility type
import type { VideoVisibility } from './video';

export type { VideoVisibility };

// Re-export centralized types for backward compatibility
export type { PlaylistSummary, CommunityPost } from './Channel';
export type { Video } from './video';
export type { Channel } from './Channel';
export type { Comment } from './Comment';

// Extended Video interface with additional properties for backward compatibility
// Using unified Video type
export type { Video as ExtendedVideo } from './video';

// Extended Channel interface for backward compatibility
export interface ExtendedChannel {
  id: string;
  name: string;
  avatarUrl: string;
  subscribers: string;
  subscriberCount?: string;
  videoCount?: number;
  isVerified?: boolean;
  description?: string;
  joinDate?: string; // Added
  totalViews?: string; // Added
  channelDescription?: string; // Added
  playlists?: import('./Channel').PlaylistSummary[];
  communityPosts?: import('./Channel').CommunityPost[];
}

// Remove ContentItem as it should come from core
export type { ContentItem } from './core';

// User-specific playlists
export interface UserPlaylist {
  id: string;
  title: string;
  description?: string;
  videoIds: string[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface UserPlaylistDetails extends UserPlaylist {
  videoCount: number;
  thumbnailUrl?: string; // Thumbnail of the first video, or a default
}

// For AI Content Spark feature
export interface VideoIdeaResponse {
  ideas: string[];
  titles: string[];
  concept: string;
  talkingPoints: string[];
  tags: string[];
  error?: string;
}

export interface VideoUploadData {
  title: string;
  description: string;
  category: string;
  tags: string[];
  visibility: 'public' | 'unlisted' | 'private';
  videoFile: File | null;
  thumbnailFile: File | null;
  isShorts: boolean;
}

export interface UploadProgress {
  percentage: number;
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  message: string;
}

// Type alias for shorts (videos with isShort: true)
export type Short = Omit<ExtendedVideo, 'isShort'> & {
  isShort: true;
  isVertical?: boolean;
  // Additional Short-specific properties
  musicInfo?: {
    title: string;
    artist: string;
    coverUrl?: string;
  };
  // Engagement metrics specific to Shorts
  shares?: number;
  saves?: number;
  // Shorts-specific features
  hasCaptions?: boolean;
  hasAudio?: boolean;
  // Analytics
  viewDuration?: number; // Average view duration in seconds
  swipeAwayRate?: number; // Percentage of viewers who swiped away
  // Additional metadata
  createdTime?: string;
  modifiedTime?: string;
  // Privacy and status
  visibility?: VideoVisibility;
  // Monetization
  isMonetized?: boolean;
  // Interactive elements
  hasInteractiveElements?: boolean;
  // Thumbnail variants
  thumbnailOverlays?: Array<{
    type: 'text' | 'image' | 'time';
    content: string;
    position: 'top' | 'bottom' | 'left' | 'right';
    style?: Record<string, any>;
  }>;
};
