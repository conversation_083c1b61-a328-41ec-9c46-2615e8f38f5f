import { useState } from 'react';

import { User, MoreVertical } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';

import { Button } from '@/components/atoms/Button';
import { cn } from '@/lib/utils';


// Simple utility functions
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

const getTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays < 1) {
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    if (diffHours < 1) {
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      return `${diffMinutes} minutes ago`;
    }
    return `${diffHours} hours ago`;
  }
  if (diffDays < 7) {
    return `${diffDays} days ago`;
  }
  if (diffDays < 30) {
    const diffWeeks = Math.floor(diffDays / 7);
    return `${diffWeeks} weeks ago`;
  }
  if (diffDays < 365) {
    const diffMonths = Math.floor(diffDays / 30);
    return `${diffMonths} months ago`;
  }
  const diffYears = Math.floor(diffDays / 365);
  return `${diffYears} years ago`;
};

// Simple image component since we're not using Next.js
const Image = ({
  src,
  alt,
  width,
  height,
  className,
  fill,
  sizes,
  ...props
}: {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fill?: boolean;
  sizes?: string;
  loading?: 'eager' | 'lazy';
} & React.ImgHTMLAttributes<HTMLImageElement>) => {
  return (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={cn(className, { 'w-full h-full object-cover': fill })}
      {...props}
    />
  );
};

export interface VideoCardProps {
  id: string;
  title: string;
  channelName: string;
  channelId: string;
  thumbnailUrl: string;
  viewCount: number;
  publishedAt: string;
  duration: number | string;
  avatarUrl?: string;
  showChannelInfo?: boolean;
  className?: string;
  onMoreClick?: (videoId: string) => void;
  variant?: 'default' | 'compact' | 'studio';
  lazy?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showChannel?: boolean;
}

export const VideoCard = ({
  id,
  title,
  channelName,
  channelId,
  thumbnailUrl,
  viewCount,
  publishedAt,
  duration,
  avatarUrl,
  showChannelInfo = true,
  className,
  onMoreClick,
  variant = 'default',
  lazy = true,
  size = 'md',
  showChannel = true,
}: VideoCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const navigate = useNavigate();

  const handleChannelNavigation = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.stopPropagation();
    e.preventDefault();
    navigate(`/channel/${channelId}`);
  };

  const handleChannelKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      handleChannelNavigation(e);
    }
  };

  // Format duration helper
  const formatDuration = (seconds: number | string): string => {
    if (typeof seconds === 'string') {
      return seconds; // Already formatted
    }

    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = Math.floor(seconds % 60);

    return [h, m, s]
      .filter((v, i) => v > 0 || i > 0)
      .map(v => v.toString().padStart(2, '0'))
      .join(':');
  };

  // Format views helper
  const formatViews = (count: number): string => {
    if (count > 999999) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count > 999) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Compact variant
  if (variant === 'compact') {
    return (
      <div className="flex space-x-2 mb-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded p-1 cursor-pointer">
        <div className="relative flex-shrink-0 w-40 h-24">
          <Link to={`/watch?v=${id}`}>
            <Image
              src={thumbnailUrl}
              alt={title}
              className="w-full h-full object-cover rounded"
              loading={lazy ? 'lazy' : 'eager'}
            />
            <div className="absolute bottom-1 right-1 bg-black bg-opacity-80 text-white text-xs px-1 rounded">
              {formatDuration(duration)}
            </div>
          </Link>
        </div>
        <div className="flex-grow min-w-0">
          <Link to={`/watch?v=${id}`} className="block">
            <h3 className="font-medium text-sm line-clamp-2 mb-1">{title}</h3>
          </Link>
          {showChannel && (
            <div
              role="button"
              tabIndex={0}
              onClick={handleChannelNavigation}
              onKeyDown={handleChannelKeyDown}
              className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 cursor-pointer"
            >
              {channelName}
            </div>
          )}
          <p className="text-xs text-gray-600 dark:text-gray-400">
            {formatViews(viewCount)} views • {getTimeAgo(publishedAt)}
          </p>
        </div>
      </div>
    );
  }

  // Studio variant
  if (variant === 'studio') {
    return (
      <div className="border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
        <div className="flex p-3">
          <div className="relative w-40 h-24 flex-shrink-0">
            <Link to={`/studio/videos/edit/${id}`}>
              <Image
                src={thumbnailUrl}
                alt={title}
                className="w-full h-full object-cover rounded"
                loading={lazy ? 'lazy' : 'eager'}
              />
              <div className="absolute bottom-1 right-1 bg-black bg-opacity-80 text-white text-xs px-1 rounded">
                {formatDuration(duration)}
              </div>
            </Link>
          </div>
          <div className="ml-3 flex-grow">
            <Link to={`/studio/videos/edit/${id}`} className="block">
              <h3 className="font-medium line-clamp-2 mb-1">{title}</h3>
            </Link>
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <span>{getTimeAgo(publishedAt)}</span>
              <span className="mx-1">•</span>
              <span>{formatViews(viewCount)} views</span>
              <span className="mx-1">•</span>
              <span className="text-green-600">public</span>
            </div>
          </div>
          <div className="ml-2 flex flex-col space-y-2">
            <button className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
              <MoreVertical className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div
      className={cn('flex flex-col space-y-2 group', className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative aspect-video rounded-xl overflow-hidden bg-gray-200 dark:bg-gray-800">
        <Link to={`/watch?v=${id}`} className="block w-full h-full">
          <Image
            src={thumbnailUrl}
            alt={title}
            fill
            className={cn(
              'object-cover transition-transform duration-300',
              isHovered ? 'scale-105' : 'scale-100',
            )}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            loading={lazy ? 'lazy' : 'eager'}
          />
        </Link>
        <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-1.5 py-0.5 rounded">
          {formatDuration(duration)}
        </div>
      </div>

      <div className="flex space-x-2">
        {showChannelInfo && showChannel && (
          <div
            role="button"
            tabIndex={0}
            onClick={handleChannelNavigation}
            onKeyDown={handleChannelKeyDown}
            className="shrink-0 cursor-pointer"
            aria-label={`Go to ${channelName} channel`}
          >
            <div className="w-9 h-9 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden">
              {avatarUrl ? (
                <Image
                  src={avatarUrl}
                  alt={channelName}
                  width={36}
                  height={36}
                  className="w-full h-full object-cover"
                  loading={lazy ? 'lazy' : 'eager'}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-600">
                  <User className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                </div>
              )}
            </div>
          </div>
        )}

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <h3 className="font-medium text-sm line-clamp-2">
              <Link to={`/watch?v=${id}`} className="hover:text-blue-600 dark:hover:text-blue-400">
                {title}
              </Link>
            </h3>
            {onMoreClick && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => onMoreClick(id)}
              >
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">More options</span>
              </Button>
            )}
          </div>

          {showChannelInfo && showChannel && (
            <div className="text-xs text-gray-600 dark:text-gray-400">
              <div
                role="button"
                tabIndex={0}
                onClick={handleChannelNavigation}
                onKeyDown={handleChannelKeyDown}
                className="hover:text-gray-800 dark:hover:text-gray-200 cursor-pointer inline-block"
                aria-label={`Go to ${channelName} channel`}
              >
                {channelName}
              </div>
            </div>
          )}

          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
            <span>{formatNumber(viewCount)} views</span>
            <span className="mx-1">•</span>
            <span>{getTimeAgo(publishedAt)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoCard;
