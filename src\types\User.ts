/**
 * Unified User Type Definition
 * 
 * This is the single source of truth for user-related types across the application.
 * Consolidates User interfaces from auth features and core types.
 */

import type { BaseEntity } from './BaseEntity';

export interface SocialLinks {
  twitter?: string;
  instagram?: string;
  facebook?: string;
  website?: string;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  subscriptions: boolean;
  comments: boolean;
  likes: boolean;
  mentions: boolean;
}

export interface PrivacySettings {
  showSubscriptions: boolean;
  showLikedVideos: boolean;
  showWatchHistory: boolean;
  allowComments: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  autoplay: boolean;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export type UserRole = 'user' | 'creator' | 'admin';

/**
 * Unified User Interface
 * 
 * This interface consolidates all user properties from different features
 * and provides a single source of truth for user data.
 */
export interface User extends BaseEntity {
  // Core identification
  username: string;
  email: string;
  displayName: string;
  
  // Profile information
  avatar?: string;
  avatarUrl?: string; // Legacy alias
  bio?: string;
  description?: string; // Legacy alias
  
  // Status and verification
  verified: boolean;
  isVerified: boolean; // Legacy alias
  role: UserRole;
  
  // Channel information
  channelId?: string;
  subscriberCount: number;
  totalViews?: number;
  
  // Profile customization
  bannerImage?: string;
  channelBanner?: string; // Legacy alias
  socialLinks?: SocialLinks;
  
  // Settings
  preferences: UserPreferences;
  
  // Temporal data
  joinedDate?: string;
  
  // Legacy fields for backward compatibility
  country?: string;
}

// Auth-specific types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  displayName: string;
  acceptTerms?: boolean;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

// Context types
export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => Promise<void>;
  refreshToken: () => Promise<void>;
}

export default User;
