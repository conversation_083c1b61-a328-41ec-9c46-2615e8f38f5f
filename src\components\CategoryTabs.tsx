import type React from 'react';

interface Category {
  id: 'all' | 'music' | 'gaming' | 'news' | 'movies';
  label: string;
  icon: string;
}

interface CategoryTabsProps {
  categories: Category[];
  activeCategory: 'all' | 'music' | 'gaming' | 'news' | 'movies';
  setActiveCategory: (category: 'all' | 'music' | 'gaming' | 'news' | 'movies') => void;
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({ categories, activeCategory, setActiveCategory }) => {
  return (
    <div className="mb-6">
      <div className="flex space-x-1 overflow-x-auto no-scrollbar">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setActiveCategory(category.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium transition-colors whitespace-nowrap ${
              activeCategory === category.id
                ? 'bg-neutral-900 dark:bg-neutral-100 text-white dark:text-neutral-900'
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-200 dark:hover:bg-neutral-700'
            }`}
          >
            <span>{category.icon}</span>
            <span>{category.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryTabs;
export type { Category };