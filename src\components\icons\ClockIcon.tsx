import type React from 'react';

const ClockIcon: React.FC<{ className?: string }> = ({ className = 'w-5 h-5' }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
  >
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);
// Note: This is the same as HistoryIcon for simplicity, can be differentiated if needed.
// For a more distinct "Watch Later" icon, YouTube often uses a clock with a play symbol or a list.
// Using a simple clock for now.

export default ClockIcon;