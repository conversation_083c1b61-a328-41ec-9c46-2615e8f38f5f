{"C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/vite-env.d.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/vite-env.d.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/main.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/main.tsx", "imports": ["react", "react-dom/client", "@/styles.css", "@/App"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/App.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/App.tsx", "imports": ["react-router-dom", "@/config/routes", "@/providers/RefactoredAppProviders"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/videoUtils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/videoUtils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/uploadUtils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/uploadUtils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/unifiedUtils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/unifiedUtils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/testing.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/testing.tsx", "imports": ["@tanstack/react-query", "@testing-library/react", "@testing-library/user-event", "react-router-dom", "vitest"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/test-setup.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/test-setup.ts", "imports": ["@testing-library/jest-dom", "@testing-library/react", "vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\mocks\\server"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/playerUtils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/playerUtils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/performance.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/performance.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/numberUtils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/numberUtils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/normalizeVideo.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/normalizeVideo.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/imageUtils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/imageUtils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/formatters.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/formatters.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/dateUtils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/dateUtils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/componentUtils.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/componentUtils.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/componentOptimizations.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/componentOptimizations.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/cn.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/cn.ts", "imports": ["clsx", "tailwind-merge"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/analyticsUtils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/analyticsUtils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/video.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/video.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/unifiedTypes.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/unifiedTypes.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/strictTypes.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/strictTypes.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/legacy.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/legacy.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/core.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/types/core.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/store/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/store/index.ts", "imports": ["zustand", "zustand/middleware", "zustand/middleware/immer"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/uploadService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/uploadService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/unifiedDataService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/unifiedDataService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\youtube-utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\youtubeService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\metadataNormalizationService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/unifiedApiService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/unifiedApiService.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/settingsService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/settingsService.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/searchService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/searchService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\adapters\\videoAdapter", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\unifiedDataService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/mockVideoService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/mockVideoService.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/mockApiService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/mockApiService.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/metadataNormalizationService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/metadataNormalizationService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\youtubeService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/googleSearchService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/googleSearchService.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/geminiService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/geminiService.ts", "imports": ["@google/genai"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/errorService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/errorService.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/analyticsService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/analyticsService.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/pages/YouTubeDemo.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/pages/YouTubeDemo.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\examples\\YouTubePlayerExample", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\youtube-utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\pages\\lib\\youtube-utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\pages\\components\\examples\\YouTubePlayerExample"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/pages/WatchPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/pages/WatchPage.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/pages/HomePage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/pages/HomePage.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/providers/UnifiedProviders.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/providers/UnifiedProviders.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\AuthContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\MiniplayerContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\ThemeContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\WatchLaterContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/providers/RefactoredAppProviders.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/providers/RefactoredAppProviders.tsx", "imports": ["@tanstack/react-query", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ErrorBoundary", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\SuspenseWrapper", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\AuthContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\OptimizedMiniplayerContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\ThemeContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\UnifiedAppContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\WatchLaterContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/providers/AppProviders.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/providers/AppProviders.tsx", "imports": ["@tanstack/react-query", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\SuspenseWrapper", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\providers\\UnifiedProviders"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/lib/youtube-utils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/lib/youtube-utils.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/lib/utils.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/lib/utils.ts", "imports": ["clsx", "tailwind-merge"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/lib/constants.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/lib/constants.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useWatchPage.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useWatchPage.ts", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\services\\unifiedDataService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideosData.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideosData.ts", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\services\\unifiedDataService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\useAsyncData"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideoPlayer.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideoPlayer.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideoData.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideoData.ts", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\numberUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\useAsyncData"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideoCache.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideoCache.ts", "imports": ["react", "@tanstack/react-query"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideoAutoplay.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useVideoAutoplay.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useSubscriptions.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useSubscriptions.ts", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useShortsData.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useShortsData.ts", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\useAsyncData"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useRefactoredHooks.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useRefactoredHooks.ts", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\UnifiedAppContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/usePerformanceMonitor.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/usePerformanceMonitor.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/usePagination.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/usePagination.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useOptimizedVideoData.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useOptimizedVideoData.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useModal.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useModal.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useLocalStorageSet.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useLocalStorageSet.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useLocalStorage.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useLocalStorage.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useIntersectionObserver.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useIntersectionObserver.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useFormState.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useFormState.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useDropdownMenu.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useDropdownMenu.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useDebounce.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useDebounce.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useCommon.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useCommon.ts", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\componentUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useAsyncState.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useAsyncState.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useAsyncData.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useAsyncData.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useAnalytics.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/useAnalytics.ts", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\analyticsService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\usePerformanceMonitor"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unifiedHooks.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unifiedHooks.ts", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/WatchLaterContext.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/WatchLaterContext.tsx", "imports": ["react", "react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/UnifiedAppContext.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/UnifiedAppContext.tsx", "imports": ["react", "react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/ThemeContext.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/ThemeContext.tsx", "imports": ["react", "react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/OptimizedMiniplayerContext.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/OptimizedMiniplayerContext.tsx", "imports": ["react", "react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/MiniplayerContext.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/MiniplayerContext.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/AuthContext.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/contexts/AuthContext.tsx", "imports": ["react", "react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/config/routes.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/config/routes.tsx", "imports": ["react", "react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/YouTubeVideoGrid.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/YouTubeVideoGrid.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\YouTubeVideoCard"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/YouTubeVideoCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/YouTubeVideoCard.tsx", "imports": ["react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\componentUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\YouTubePlayerWrapper"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/YouTubePlayerWrapper.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/YouTubePlayerWrapper.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\settingsService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\YouTubePlayer"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/YouTubePlayer.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/YouTubePlayer.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\lib\\youtube-utils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/WatchHistory.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/WatchHistory.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\HistoryIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VirtualizedVideoGrid.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VirtualizedVideoGrid.tsx", "imports": ["react", "react-window", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\LoadingSpinner", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\OptimizedVideoCard"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoQualitySelector.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoQualitySelector.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoPlaybackDetails.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoPlaybackDetails.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\lib\\youtube-utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\numberUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\AdvancedVideoPlayer", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\VideoActions", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\VideoDescription", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\YouTubePlayerWrapper"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoMetadata.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoMetadata.tsx", "imports": ["@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoGrid.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoGrid.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\VideoCard"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoEditor.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoEditor.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoDescription.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoDescription.tsx", "imports": ["@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\numberUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\SummarizeIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoCard.tsx", "imports": ["react", "lucide-react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\atoms\\Button"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoAnalyticsDashboard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoAnalyticsDashboard.tsx", "imports": ["react", "@heroicons/react/24/outline", "chart.js", "react-chartjs-2"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoAnalytics.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoAnalytics.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoActions.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/VideoActions.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\numberUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\SaveIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\SaveIconFilled", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\ThumbsDownIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\ThumbsUpIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/UserMenu.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/UserMenu.tsx", "imports": ["@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\AuthContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\ThemeContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/UnifiedVideoCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/UnifiedVideoCard.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\useIntersectionObserver", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components\\unified\\UnifiedButton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\unifiedUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/TrendingSection.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/TrendingSection.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\FireIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SuspenseWrapper.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SuspenseWrapper.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\LoadingSpinner"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionVideoCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionVideoCard.tsx", "imports": ["@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\WatchLaterContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\unifiedHooks", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\componentUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\UnifiedIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ui\\Button"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionStats.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionStats.tsx", "imports": ["@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionManager.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionManager.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionFeed.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SubscriptionFeed.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\SubscriptionsIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/StudioLayout.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/StudioLayout.tsx", "imports": ["react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\StudioSidebar", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\StudioHeader"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/StandardPageLayout.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/StandardPageLayout.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\LoadingSpinner", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\LoadingStates"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SortFilterPanel.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SortFilterPanel.tsx", "imports": ["react", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\AdjustmentsHorizontalIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Sidebar.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Sidebar.tsx", "imports": ["react", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\ClockIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\FireIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\HistoryIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\HomeIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\PlaylistIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\PlaylistPlayIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\ShortsIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\SubscriptionsIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsSection.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsSection.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\ShortsIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ShortDisplayCard"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsProgressIndicator.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsProgressIndicator.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsPlayer.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsPlayer.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsNavigation.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsNavigation.tsx", "imports": ["@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsFilters.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortsFilters.tsx", "imports": ["@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortDisplayCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ShortDisplayCard.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\hooks\\useVideoPlayer", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ui"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SearchSuggestions.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SearchSuggestions.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\SearchIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SearchResults.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SearchResults.tsx", "imports": ["react", "react-window", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\hooks\\useIntersectionObserver", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\utils\\componentOptimizations", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\utils\\performance", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\VideoCard"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SearchBar.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/SearchBar.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\components\\icons\\ClockIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\components\\icons\\SearchIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\components\\SearchSuggestions", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ReusableVideoGrid.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ReusableVideoGrid.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\LoadingStates", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\OptimizedVideoCard"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/RefactoredVideoDescription.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/RefactoredVideoDescription.tsx", "imports": ["@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\numberUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\SummarizeIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/RefactoredSaveToPlaylistModal.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/RefactoredSaveToPlaylistModal.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\BaseForm", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\BaseModal"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/RecommendationEngine.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/RecommendationEngine.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ProtectedRoute.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ProtectedRoute.tsx", "imports": ["react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\AuthContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/PlaylistEditModal.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/PlaylistEditModal.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/PictureInPicture.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/PictureInPicture.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/PageLayout.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/PageLayout.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\DataWrapper"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/OptimizedVideoCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/OptimizedVideoCard.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\OptimizedMiniplayerContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\WatchLaterContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\useDropdownMenu", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\useIntersectionObserver", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\lib\\youtube-utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\componentOptimizations", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\formatters", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\performance", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ui\\DropdownMenu", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\YouTubePlayer"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/NotificationSystem.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/NotificationSystem.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/NotificationsPanel.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/NotificationsPanel.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/NotificationCenter.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/NotificationCenter.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ModerationDashboard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ModerationDashboard.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Modal.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Modal.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Miniplayer.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Miniplayer.tsx", "imports": ["@heroicons/react/24/solid", "react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates.tsx", "imports": ["@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingSpinner.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingSpinner.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LiveStreams.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LiveStreams.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LiveStreamManager.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LiveStreamManager.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Layout.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Layout.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\OptimizedMiniplayerContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\Header", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\Miniplayer", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\Sidebar"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ImageWithFallback.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ImageWithFallback.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/HomeContent.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/HomeContent.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\LiveStreams", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ShortsSection", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\SubscriptionFeed", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\TrendingSection", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\WatchHistory"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Header.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/Header.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\contexts\\AuthContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\forms\\Button", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\MenuIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\VideoPlusIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\YouTubeLogo", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\NotificationSystem", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\UserMenu"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ErrorBoundary.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ErrorBoundary.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/EnhancedVideoUpload.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/EnhancedVideoUpload.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/EnhancedCommentSystem.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/EnhancedCommentSystem.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/DataWrapper.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/DataWrapper.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\LoadingStates"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ConsolidatedVideoCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ConsolidatedVideoCard.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\useRefactoredHooks", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CommunityPosts.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CommunityPosts.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CommentsSection.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CommentsSection.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\AddCommentForm", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\ThumbsDownIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\ThumbsUpIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CommentModal.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CommentModal.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\BaseModal"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ChannelTabs.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ChannelTabs.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ChannelTabContent.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ChannelTabContent.tsx", "imports": ["@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ChannelHeader.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ChannelHeader.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\icons\\BellIcon"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CategoryTabs.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CategoryTabs.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CategoryChips.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/CategoryChips.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/BaseModal.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/BaseModal.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/BaseForm.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/BaseForm.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/AdvancedVideoPlayer.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/AdvancedVideoPlayer.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/AdvancedSearch.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/AdvancedSearch.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/AddCommentForm.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/AddCommentForm.tsx", "imports": ["react", "@heroicons/react/24/solid"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/AccountLayout.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/AccountLayout.tsx", "imports": ["@heroicons/react/24/outline", "react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/mocks/youtubeApiTestHandlers.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/mocks/youtubeApiTestHandlers.ts", "imports": ["msw"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/mocks/server.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/mocks/server.ts", "imports": ["msw/node", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\mocks\\handlers"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/mocks/handlers.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/mocks/handlers.ts", "imports": ["msw"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/mocks/browser.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/utils/mocks/browser.ts", "imports": ["msw/browser", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\mocks\\handlers"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/uploadService.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/uploadService.test.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\uploadService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/unifiedDataService.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/unifiedDataService.test.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\youtubeService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\metadataNormalizationService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\unifiedDataService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/unifiedDataService.simple.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/unifiedDataService.simple.test.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\unifiedDataService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/searchService.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/searchService.test.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\searchService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/extractYouTubeId.spec.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/extractYouTubeId.spec.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\unifiedDataService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/analyticsService.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/__tests__/analyticsService.test.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\analyticsService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/youtubeService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/youtubeService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\constants", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/videos.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/videos.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/base.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/base.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\constants"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/adapters/videoAdapter.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/adapters/videoAdapter.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unified/useVideos.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unified/useVideos.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\videos", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\metadataNormalizationService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\unifiedDataService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\unified\\useApi"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unified/useApi.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unified/useApi.ts", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\constants"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unified/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unified/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/config/config/routes.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/config/config/routes.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\config\\components\\AccountLayout", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\config\\components\\ErrorBoundary", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\config\\components\\Layout", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\config\\components\\ProtectedRoute", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\config\\components\\StudioLayout"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/config/config/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/config/config/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/__tests__/ErrorBoundary.test.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/__tests__/ErrorBoundary.test.tsx", "imports": ["@testing-library/react", "vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ErrorBoundary"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/unified/UnifiedVideoCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/unified/UnifiedVideoCard.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\unified\\UnifiedButton"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/unified/UnifiedButton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/unified/UnifiedButton.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\components\\ui\\LoadingSpinner", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\utils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/unified/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/unified/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/video/VideoSettings.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/video/VideoSettings.tsx", "imports": ["@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/video/VideoControls.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/video/VideoControls.tsx", "imports": ["@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ui"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/video/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/video/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/UnifiedComponents.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/UnifiedComponents.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/UnifiedButton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/UnifiedButton.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/Tabs.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/Tabs.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/ProgressBar.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/ProgressBar.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/LoadingStates.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/LoadingStates.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/LoadingSpinner.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/LoadingSpinner.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/FormComponents.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/FormComponents.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/FileUpload.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/FileUpload.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/ErrorMessage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/ErrorMessage.tsx", "imports": ["@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/DropdownMenu.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/DropdownMenu.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/Button.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/Button.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ui\\LoadingStates"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/Badge.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/Badge.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/ActionButton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ui/ActionButton.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/molecules/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/molecules/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/organisms/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/organisms/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/WatchPageSkeleton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/WatchPageSkeleton.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/ShortsPageSkeleton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/ShortsPageSkeleton.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/PlaylistDetailSkeleton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/PlaylistDetailSkeleton.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/LikedVideosPageSkeleton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/LikedVideosPageSkeleton.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/HistoryPageSkeleton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/HistoryPageSkeleton.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/ChannelPageSkeleton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/LoadingStates/ChannelPageSkeleton.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/YouTubeLogo.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/YouTubeLogo.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/VideoPlusIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/VideoPlusIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/UserIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/UserIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/UnifiedIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/UnifiedIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ThumbsUpIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ThumbsUpIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ThumbsDownIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ThumbsDownIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SummarizeIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SummarizeIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SubscriptionsIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SubscriptionsIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ShortsIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ShortsIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ShareIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ShareIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SearchIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SearchIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SaveIconFilled.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SaveIconFilled.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SaveIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/SaveIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/PlaylistPlayIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/PlaylistPlayIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/PlaylistIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/PlaylistIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/MenuIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/MenuIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/HomeIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/HomeIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/HistoryIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/HistoryIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/HeartIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/HeartIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/FireIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/FireIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ClockIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ClockIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ChatBubbleOvalLeftEllipsisIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ChatBubbleOvalLeftEllipsisIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/BellIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/BellIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ArrowUturnRightIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/ArrowUturnRightIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/AdjustmentsHorizontalIcon.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/icons/AdjustmentsHorizontalIcon.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/examples/YouTubePlayerExample.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/examples/YouTubePlayerExample.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\youtube-utils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/forms/UnifiedFormSystem.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/forms/UnifiedFormSystem.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\cn", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\ui\\UnifiedButton"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/forms/Textarea.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/forms/Textarea.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/forms/Input.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/forms/Input.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/forms/Button.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/forms/Button.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ErrorStates/VideoNotFound.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ErrorStates/VideoNotFound.tsx", "imports": ["react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ErrorStates/ShortsPageError.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ErrorStates/ShortsPageError.tsx", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ErrorStates/EmptyShortsState.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/ErrorStates/EmptyShortsState.tsx", "imports": ["@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/atoms/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/atoms/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/__tests__/youtubeService.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/__tests__/youtubeService.test.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\youtubeService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/__tests__/buildUrl.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/api/__tests__/buildUrl.test.ts", "imports": ["vitest"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/adapters/__tests__/videoAdapter.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/services/adapters/__tests__/videoAdapter.test.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\adapters\\videoAdapter"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unified/__tests__/useVideos.test.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/hooks/unified/__tests__/useVideos.test.tsx", "imports": ["@tanstack/react-query", "@testing-library/react", "vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\unifiedDataService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\unified\\useVideos"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/types/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/types/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/services/videoService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/services/videoService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\adapters\\videoAdapter", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\youtubeService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\unifiedDataService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/services/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/services/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/mocks/videoMocks.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/mocks/videoMocks.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/WatchPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/WatchPage.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\VideoMetadata", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\contexts\\OptimizedMiniplayerContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\contexts\\WatchLaterContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\hooks\\useWatchPage", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\services\\settingsService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\src\\lib\\youtube-utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/VideoDemo.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/VideoDemo.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\mocks\\videoMocks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/TrendingPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/TrendingPage.tsx", "imports": ["react", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\CategoryTabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\PageLayout", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\hooks", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/ShortsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/ShortsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\CommentModal", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\ErrorStates\\EmptyShortsState", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\ErrorStates\\ShortsPageError", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\LoadingStates\\ShortsPageSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\ShortDisplayCard", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\ShortsFilters", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\ShortsNavigation", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/SearchResultsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/SearchResultsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\hooks\\useDebounce", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\services\\api", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\services\\googleSearchService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\utils\\performance"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/HomePage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/pages/HomePage.tsx", "imports": ["react", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\CategoryChips", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\HomeContent", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\components\\PageLayout", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/hooks/useVideoInteractions.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/hooks/useVideoInteractions.ts", "imports": ["react", "@tanstack/react-query", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\services\\videoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/hooks/useVideo.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/hooks/useVideo.ts", "imports": ["@tanstack/react-query", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\unifiedDataService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\services\\videoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/hooks/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/hooks/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoUpload.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoUpload.tsx", "imports": ["react", "react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoPlayer.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoPlayer.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoList.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoList.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoInteractions.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoInteractions.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\hooks\\useVideoInteractions"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoEditor.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/VideoEditor.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/StudioVideoGrid.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/StudioVideoGrid.tsx", "imports": ["react", "react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/components/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/YourDataPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/YourDataPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/WatchLaterPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/WatchLaterPage.tsx", "imports": ["@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\contexts\\WatchLaterContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/SubscriptionsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/SubscriptionsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\icons\\SubscriptionsIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\SubscriptionStats", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\SubscriptionVideoCard", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\ui\\Button", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\ui\\LoadingSpinner", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\ui\\Tabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/SettingsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/SettingsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\contexts\\ThemeContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/LikedVideosPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/LikedVideosPage.tsx", "imports": ["react", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\LoadingStates\\LikedVideosPageSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/LibraryPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/LibraryPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\icons\\HistoryIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\icons\\PlaylistIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/HistoryPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/user/pages/HistoryPage.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\icons\\HistoryIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\components\\LoadingStates\\HistoryPageSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\user\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/subscription/services/subscriptionService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/subscription/services/subscriptionService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/subscription/hooks/useSubscription.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/subscription/hooks/useSubscription.ts", "imports": ["@tanstack/react-query", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\subscription\\services\\subscriptionService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/subscription/components/SubscriptionButton.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/subscription/components/SubscriptionButton.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\subscription\\hooks\\useSubscription"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/studio/pages/StudioPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/studio/pages/StudioPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\studio\\components\\ui\\Tabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\studio\\components\\ui\\UnifiedButton"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/studio/pages/StudioDashboardPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/studio/pages/StudioDashboardPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\studio\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\studio\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/services/playlistService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/services/playlistService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/pages/PlaylistsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/pages/PlaylistsPage.tsx", "imports": ["react", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\playlist\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/pages/PlaylistDetailPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/pages/PlaylistDetailPage.tsx", "imports": ["react", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\playlist\\components\\LoadingStates\\PlaylistDetailSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\playlist\\components\\PlaylistEditModal", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\playlist\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/search/components/AdvancedSearchFilters.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/search/components/AdvancedSearchFilters.tsx", "imports": ["@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/search/services/searchService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/search/services/searchService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/hooks/usePlaylists.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/hooks/usePlaylists.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\unified\\useApi", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\playlist\\services\\playlistService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/components/PlaylistManager.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/components/PlaylistManager.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\playlist\\hooks\\usePlaylists"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/components/PlaylistCard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/playlist/components/PlaylistCard.tsx", "imports": ["react", "react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\components\\unified", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\utils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/notifications/hooks/useNotifications.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/notifications/hooks/useNotifications.ts", "imports": ["react", "@tanstack/react-query", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\notifications\\services\\notificationService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/notifications/services/notificationService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/notifications/services/notificationService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/notifications/components/NotificationCenter.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/notifications/components/NotificationCenter.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "date-fns", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\notifications\\hooks\\useNotifications"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/moderation/pages/CommentModerationPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/moderation/pages/CommentModerationPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\moderation\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\moderation\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/moderation/pages/AdminPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/moderation/pages/AdminPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\moderation\\services\\settingsService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/moderation/components/ModerationDashboard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/moderation/components/ModerationDashboard.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/VideoUploadPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/VideoUploadPage.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\creator\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/VideoEditorPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/VideoEditorPage.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/UploadPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/UploadPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\creator\\components\\ui\\Tabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\creator\\components\\ui\\UnifiedButton"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/GoLivePage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/GoLivePage.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/DashboardPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/DashboardPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\utils\\unifiedUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/CreatorStudioPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/CreatorStudioPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\creator\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\creator\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/AIContentSparkPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/pages/AIContentSparkPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\creator\\services\\geminiService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/components/CreatorStudioDashboard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/creator/components/CreatorStudioDashboard.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/community/pages/CommunityPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/community/pages/CommunityPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/StudioSidebar.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/StudioSidebar.tsx", "imports": ["react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\store\\authStore"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/StudioLayout.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/StudioLayout.tsx", "imports": ["react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\common\\components\\StudioHeader", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\common\\components\\StudioSidebar"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/StudioHeader.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/StudioHeader.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\store\\authStore"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/Sidebar.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/Sidebar.tsx", "imports": ["react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\store\\authStore"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/Layout.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/Layout.tsx", "imports": ["react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\common\\components\\Footer", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\common\\components\\Header", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\common\\components\\Sidebar"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/Header.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/Header.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\store\\authStore"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/Footer.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/Footer.tsx", "imports": ["react-router-dom"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/ErrorBoundary.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/common/components/ErrorBoundary.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/livestream/components/LiveStreamStudio.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/livestream/components/LiveStreamStudio.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/community/components/CommunityPost.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/community/components/CommunityPost.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "date-fns"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/YourDataPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/YourDataPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/WatchPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/WatchPage.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\VideoMetadata", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\contexts\\OptimizedMiniplayerContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\contexts\\WatchLaterContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\hooks\\useWatchPage", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\settingsService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\src\\lib\\youtube-utils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/WatchLaterPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/WatchLaterPage.tsx", "imports": ["@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\contexts\\WatchLaterContext", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/VideoUploadPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/VideoUploadPage.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/VideoEditorPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/VideoEditorPage.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/UserPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/UserPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/UploadPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/UploadPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\Tabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\UnifiedButton"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/TrendingPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/TrendingPage.tsx", "imports": ["react", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\CategoryTabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\PageLayout", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\hooks", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/SubscriptionsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/SubscriptionsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\icons\\SubscriptionsIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\SubscriptionStats", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\SubscriptionVideoCard", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\Button", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\LoadingSpinner", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\Tabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/StudioPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/StudioPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\Tabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\UnifiedButton"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/StudioDashboardPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/StudioDashboardPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/ShortsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/ShortsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\CommentModal", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ErrorStates\\EmptyShortsState", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ErrorStates\\ShortsPageError", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\LoadingStates\\ShortsPageSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ShortDisplayCard", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ShortsFilters", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ShortsNavigation", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/SettingsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/SettingsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\contexts\\ThemeContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/SearchResultsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/SearchResultsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\hooks\\useDebounce", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\api", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\googleSearchService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\src\\components", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\performance"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/RegisterPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/RegisterPage.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\forms\\Button", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\forms\\Input", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\icons\\YouTubeLogo", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\contexts\\AuthContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/RefactoredTrendingPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/RefactoredTrendingPage.tsx", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ReusableVideoGrid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\StandardPageLayout", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/RefactoredContentManagerPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/RefactoredContentManagerPage.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\BaseForm", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\BaseModal", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ReusableVideoGrid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\StandardPageLayout", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\Button", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ui\\Tabs"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/PlaylistsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/PlaylistsPage.tsx", "imports": ["react", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/PlaylistManagerPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/PlaylistManagerPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-beautiful-dnd"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/PlaylistDetailPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/PlaylistDetailPage.tsx", "imports": ["react", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\LoadingStates\\PlaylistDetailSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\PlaylistEditModal", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/OptimizedHomePage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/OptimizedHomePage.tsx", "imports": ["react", "react", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\CategoryChips", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ErrorBoundary", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\LoadingSpinner", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\VirtualizedVideoGrid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\hooks\\useOptimizedVideoData", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\cn"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/MonetizationPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/MonetizationPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "chart.js", "react-chartjs-2"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/LoginPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/LoginPage.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\forms\\Button", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\forms\\Input", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\icons\\YouTubeLogo", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\contexts\\AuthContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/LikedVideosPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/LikedVideosPage.tsx", "imports": ["react", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\LoadingStates\\LikedVideosPageSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/LibraryPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/LibraryPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\icons\\HistoryIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\icons\\PlaylistIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/HomePage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/HomePage.tsx", "imports": ["react", "@heroicons/react/24/solid", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\CategoryChips", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\HomeContent", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\PageLayout", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\hooks"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/HistoryPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/HistoryPage.tsx", "imports": ["react", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\icons\\HistoryIcon", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\LoadingStates\\HistoryPageSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/GoLivePage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/GoLivePage.tsx", "imports": ["react"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/CreatorStudioPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/CreatorStudioPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/ContentManagerPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/ContentManagerPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\dateUtils", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/CommunityPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/CommunityPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/CommentModerationPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/CommentModerationPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\dateUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/ChannelPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/ChannelPage.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ChannelHeader", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ChannelTabContent", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\ChannelTabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\components\\LoadingStates\\ChannelPageSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/ChannelCustomizationPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/ChannelCustomizationPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/AnalyticsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/AnalyticsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/AIContentSparkPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/AIContentSparkPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\geminiService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/AdminPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/misc/pages/AdminPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\misc\\services\\settingsService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/comments/services/commentService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/comments/services/commentService.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\services\\api\\base"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/comments/components/CommentSection.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/comments/components/CommentSection.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "date-fns", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\comments\\hooks\\useComments"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/comments/hooks/useComments.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/comments/hooks/useComments.ts", "imports": ["C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\hooks\\unified\\useApi", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\comments\\services\\commentService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/types/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/types/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/store/authStore.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/store/authStore.ts", "imports": ["zustand", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\services\\authService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/services/authService.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/services/authService.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/pages/RegisterPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/pages/RegisterPage.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\components\\forms\\Button", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\components\\forms\\Input", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\components\\icons\\YouTubeLogo", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\contexts\\AuthContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/pages/LoginPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/pages/LoginPage.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\components\\forms\\Button", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\components\\forms\\Input", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\components\\icons\\YouTubeLogo", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\contexts\\AuthContext"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/components/RegisterForm.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/components/RegisterForm.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\store\\authStore"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/components/ProtectedRoute.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/components/ProtectedRoute.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\components\\ui\\LoadingSpinner", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\store\\authStore"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/components/LoginForm.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/auth/components/LoginForm.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\auth\\store\\authStore"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/analytics/components/AdvancedAnalyticsDashboard.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/analytics/components/AdvancedAnalyticsDashboard.tsx", "imports": ["react", "@heroicons/react/24/outline"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/analytics/pages/AnalyticsPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/analytics/pages/AnalyticsPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\analytics\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\analytics\\utils\\numberUtils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/channel/pages/UserPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/channel/pages/UserPage.tsx", "imports": ["react", "@heroicons/react/24/outline", "@heroicons/react/24/solid", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\channel\\services\\mockVideoService", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\channel\\src\\components"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/channel/pages/ChannelPage.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/channel/pages/ChannelPage.tsx", "imports": ["react", "react-router-dom", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\channel\\components\\ChannelHeader", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\channel\\components\\ChannelTabContent", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\channel\\components\\ChannelTabs", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\channel\\components\\LoadingStates\\ChannelPageSkeleton", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\channel\\services\\mockVideoService"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/atoms/Button/index.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/atoms/Button/index.ts", "imports": [], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/atoms/Button/Button.tsx": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/components/atoms/Button/Button.tsx", "imports": ["react", "class-variance-authority", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\lib\\utils"], "importedBy": []}, "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/services/__tests__/videoService.test.ts": {"path": "C:/Users/<USER>/Documents/GitHub/yt/ytmain5/src/features/video/services/__tests__/videoService.test.ts", "imports": ["vitest", "C:\\Users\\<USER>\\Documents\\GitHub\\yt\\ytmain5\\src\\features\\video\\services\\videoService"], "importedBy": []}}