import type { Video, Playlist } from '@/types';

// Mock video data for playlists
const mockVideos: Video[] = [
  {
    id: 'video1',
    title: 'Sample Video 1',
    description: 'This is a sample video description',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    videoUrl: 'https://example.com/video1.mp4',
    duration: '10:30',
    views: '1.2M',
    viewCount: 1200000,
    likes: 15000,
    dislikes: 200,
    commentCount: 500,
    publishedAt: '2024-01-15T10:00:00Z',
    channelId: 'channel1',
    channelName: 'Sample Channel',
    channelAvatarUrl: 'https://via.placeholder.com/40x40',
    isVerified: true,
    category: 'Entertainment',
    tags: ['sample', 'video', 'entertainment'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 'video2',
    title: 'Sample Video 2',
    description: 'Another sample video description',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    videoUrl: 'https://example.com/video2.mp4',
    duration: '8:45',
    views: '850K',
    viewCount: 850000,
    likes: 12000,
    dislikes: 150,
    commentCount: 300,
    publishedAt: '2024-01-14T15:30:00Z',
    channelId: 'channel1',
    channelName: 'Sample Channel',
    channelAvatarUrl: 'https://via.placeholder.com/40x40',
    isVerified: true,
    category: 'Education',
    tags: ['sample', 'educational', 'tutorial'],
    createdAt: '2024-01-14T15:30:00Z',
    updatedAt: '2024-01-14T15:30:00Z',
  },
];

// Mock playlist data
const mockPlaylists: Playlist[] = [
  {
    id: 'playlist1',
    title: 'My Favorite Videos',
    description: 'A collection of my favorite videos',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    visibility: 'public',
    videos: [
      { videoId: 'video1', position: 0, addedAt: '2024-01-15T10:00:00Z' },
      { videoId: 'video2', position: 1, addedAt: '2024-01-14T15:30:00Z' },
    ],
    totalDuration: 1155, // in seconds
    totalViews: 2050000,
    channelId: 'channel1',
    channelName: 'Sample Channel',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
];

export const getVideosByIds = async (videoIds: string[]): Promise<Video[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return mockVideos.filter(video => videoIds.includes(video.id));
};

export const getPlaylistById = async (playlistId: string): Promise<Playlist | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return mockPlaylists.find(playlist => playlist.id === playlistId) || null;
};

export const getUserPlaylists = async (): Promise<Playlist[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return mockPlaylists;
};

export const createPlaylist = async (playlistData: Partial<Playlist>): Promise<Playlist> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const newPlaylist: Playlist = {
    id: `playlist_${Date.now()}`,
    title: playlistData.title || 'Untitled Playlist',
    description: playlistData.description || '',
    thumbnailUrl: 'https://via.placeholder.com/320x180',
    visibility: playlistData.visibility || 'private',
    videos: [],
    totalDuration: 0,
    totalViews: 0,
    channelId: 'channel1',
    channelName: 'Sample Channel',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  mockPlaylists.push(newPlaylist);
  return newPlaylist;
};

export const updatePlaylist = async (
  playlistId: string, 
  updates: Partial<Playlist>
): Promise<Playlist | null> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const playlistIndex = mockPlaylists.findIndex(p => p.id === playlistId);
  if (playlistIndex === -1) return null;
  
  mockPlaylists[playlistIndex] = {
    ...mockPlaylists[playlistIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  };
  
  return mockPlaylists[playlistIndex];
};

export const deletePlaylist = async (playlistId: string): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const playlistIndex = mockPlaylists.findIndex(p => p.id === playlistId);
  if (playlistIndex === -1) return false;
  
  mockPlaylists.splice(playlistIndex, 1);
  return true;
};
