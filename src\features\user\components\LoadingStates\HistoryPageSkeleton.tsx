import React from 'react';

interface HistoryPageSkeletonProps {
  className?: string;
}

export const HistoryPageSkeleton: React.FC<HistoryPageSkeletonProps> = ({ 
  className = '' 
}) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {/* Header skeleton */}
      <div className="mb-6">
        <div className="h-8 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="h-4 bg-gray-300 rounded w-1/2"></div>
      </div>

      {/* Filter controls skeleton */}
      <div className="flex space-x-4 mb-6">
        <div className="h-10 bg-gray-300 rounded w-32"></div>
        <div className="h-10 bg-gray-300 rounded w-32"></div>
        <div className="h-10 bg-gray-300 rounded w-32"></div>
      </div>

      {/* Video list skeleton */}
      <div className="space-y-4">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="flex space-x-4">
            <div className="w-40 h-24 bg-gray-300 rounded"></div>
            <div className="flex-1">
              <div className="h-5 bg-gray-300 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-300 rounded w-1/4 mb-1"></div>
              <div className="h-3 bg-gray-300 rounded w-1/3"></div>
            </div>
            <div className="w-8 h-8 bg-gray-300 rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HistoryPageSkeleton;
