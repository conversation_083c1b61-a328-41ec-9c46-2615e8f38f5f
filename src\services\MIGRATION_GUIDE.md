# Service Migration Guide

This guide helps you migrate components from ad-hoc fetch logic to centralized services.

## Overview

All fetch logic should now be centralized in the service layer to:
- Ensure consistent return types (unified `Video` type)
- Reduce code duplication
- Improve testability
- Standardize error handling

## Available Services

### Core Services
- `searchService` - All search functionality
- `analyticsService` - Data analytics
- `uploadService` - File uploads  
- `unifiedDataService` - Cross-platform video data
- `youtubeService` - YouTube API wrapper

### Quick Import
```typescript
import { searchService, analyticsService, uploadService } from '../services';
```

## Migration Examples

### Before: Ad-hoc fetch in component
```typescript
// ❌ Old way - Direct fetch in component
const [videos, setVideos] = useState<Video[]>([]);

useEffect(() => {
  const fetchVideos = async () => {
    try {
      const response = await fetch('/api/videos/trending');
      const data = await response.json();
      setVideos(data);
    } catch (error) {
      console.error('Failed to fetch videos:', error);
    }
  };
  
  fetchVideos();
}, []);
```

### After: Using centralized service
```typescript
// ✅ New way - Using centralized service
import { videoService } from '../services/video/videoService';

const [videos, setVideos] = useState<Video[]>([]);

useEffect(() => {
  const fetchVideos = async () => {
    try {
      const trendingVideos = await videoService.getTrendingVideos();
      setVideos(trendingVideos);
    } catch (error) {
      console.error('Failed to fetch videos:', error);
    }
  };
  
  fetchVideos();
}, []);
```

### Search Migration

#### Before
```typescript
// ❌ Old way
const searchVideos = async (query: string) => {
  const response = await fetch(`/api/search?q=${query}`);
  const data = await response.json();
  return data;
};
```

#### After
```typescript
// ✅ New way
import { searchService } from '../services';

const searchVideos = async (query: string) => {
  return await searchService.searchVideos(query, {
    type: 'video',
    sortBy: 'relevance'
  });
};
```

### Analytics Migration

#### Before
```typescript
// ❌ Old way
const getVideoStats = async (videoId: string) => {
  const response = await fetch(`/api/analytics/videos/${videoId}`);
  return response.json();
};
```

#### After
```typescript
// ✅ New way
import { analyticsService } from '../services';

const getVideoStats = async (videoId: string) => {
  return await analyticsService.fetchVideoAnalytics(videoId);
};
```

### Upload Migration

#### Before
```typescript
// ❌ Old way
const uploadVideo = async (file: File) => {
  const formData = new FormData();
  formData.append('video', file);
  
  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData
  });
  
  return response.json();
};
```

#### After
```typescript
// ✅ New way
import { uploadService } from '../services';

const uploadVideo = async (file: File) => {
  return await uploadService.uploadVideo(file);
};
```

## Type Safety

All services return properly typed responses using the unified `Video` type:

```typescript
import type { Video } from '../types/video';

// All these return Video[] or Video
const videos: Video[] = await searchService.searchVideos('query');
const video: Video | null = await videoService.getVideo('id');
const trending: Video[] = await videoService.getTrendingVideos();
```

## Error Handling

Services include built-in error handling and logging:

```typescript
// Services handle errors gracefully
const videos = await searchService.searchVideos('query');
// Returns empty array [] on error instead of throwing

// For explicit error handling
try {
  const analytics = await analyticsService.fetchVideoAnalytics('id');
} catch (error) {
  // Handle specific errors
  console.error('Analytics fetch failed:', error);
}
```

## Testing

Services are fully tested and mockable:

```typescript
// In tests
vi.mock('../services', () => ({
  searchService: {
    searchVideos: vi.fn().mockResolvedValue([mockVideo])
  }
}));
```

## Component Patterns

### With Custom Hook
```typescript
// Create a custom hook
const useSearchVideos = (query: string) => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    if (!query) return;
    
    const search = async () => {
      setLoading(true);
      try {
        const results = await searchService.searchVideos(query);
        setVideos(results);
      } catch (error) {
        console.error('Search failed:', error);
      } finally {
        setLoading(false);
      }
    };
    
    search();
  }, [query]);
  
  return { videos, loading };
};

// Use in component
const SearchPage = () => {
  const [query, setQuery] = useState('');
  const { videos, loading } = useSearchVideos(query);
  
  return (
    <div>
      <input value={query} onChange={(e) => setQuery(e.target.value)} />
      {loading ? <div>Loading...</div> : <VideoGrid videos={videos} />}
    </div>
  );
};
```

## Migration Checklist

- [ ] Replace all direct `fetch()` calls with service methods
- [ ] Update import statements to use centralized services
- [ ] Update type annotations to use unified `Video` type
- [ ] Remove duplicate error handling (services handle this)
- [ ] Update tests to mock services instead of fetch
- [ ] Verify all API endpoints use consistent response format

## Common Patterns to Replace

1. **Direct fetch calls** → Use appropriate service method
2. **Custom API utilities** → Use `api` from `services/api/base`
3. **Scattered type definitions** → Use unified `Video` type
4. **Manual error handling** → Services provide consistent error handling
5. **Response transformation** → Services return properly formatted data

This migration ensures:
- ✅ Type safety across the application
- ✅ Consistent API responses
- ✅ Better testability
- ✅ Reduced code duplication
- ✅ Centralized error handling
