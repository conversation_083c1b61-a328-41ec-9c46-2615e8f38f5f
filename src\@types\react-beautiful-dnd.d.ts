/**
 * Ambient type declarations for react-beautiful-dnd
 * This provides basic types when @types/react-beautiful-dnd is not sufficient
 */

declare module 'react-beautiful-dnd' {
  import { Component, ReactElement } from 'react';

  export interface DragResult {
    draggableId: string;
    type: string;
    source: DragLocation;
    destination: DragLocation | null;
    reason: DropReason;
    mode: MovementMode;
    combine: DragCombine | null;
  }

  export interface DragLocation {
    droppableId: string;
    index: number;
  }

  export interface DragCombine {
    draggableId: string;
    droppableId: string;
  }

  export type DropReason = 'DROP' | 'CANCEL';
  export type MovementMode = 'FLUID' | 'SNAP';

  export interface DragDropContextProps {
    onDragEnd: (result: DragResult) => void;
    onDragStart?: (initial: DragStart) => void;
    onDragUpdate?: (update: DragUpdate) => void;
    children: ReactElement | ReactElement[];
  }

  export interface DragStart {
    draggableId: string;
    type: string;
    source: DragLocation;
    mode: MovementMode;
  }

  export interface DragUpdate extends DragStart {
    destination?: DragLocation | null;
    combine?: Drag<PERSON>ombine | null;
  }

  export interface DroppableProps {
    droppableId: string;
    type?: string;
    direction?: 'vertical' | 'horizontal';
    isDropDisabled?: boolean;
    isCombineEnabled?: boolean;
    ignoreContainerClipping?: boolean;
    renderClone?: (provided: DraggableProvided, snapshot: DraggableStateSnapshot, rubric: DraggableRubric) => ReactElement;
    getContainerForClone?: () => HTMLElement;
    children: (provided: DroppableProvided, snapshot: DroppableStateSnapshot) => ReactElement;
  }

  export interface DroppableProvided {
    innerRef: (element?: HTMLElement | null) => void;
    droppableProps: {
      'data-rbd-droppable-context-id': string;
      'data-rbd-droppable-id': string;
    };
    placeholder?: ReactElement | null;
  }

  export interface DroppableStateSnapshot {
    isDraggingOver: boolean;
    draggingOverWith?: string | null;
    draggingFromThisWith?: string | null;
    isUsingPlaceholder: boolean;
  }

  export interface DraggableProps {
    draggableId: string;
    index: number;
    isDragDisabled?: boolean;
    disableInteractiveElementBlocking?: boolean;
    shouldRespectForcePress?: boolean;
    children: (provided: DraggableProvided, snapshot: DraggableStateSnapshot) => ReactElement;
  }

  export interface DraggableProvided {
    innerRef: (element?: HTMLElement | null) => void;
    draggableProps: {
      'data-rbd-draggable-context-id': string;
      'data-rbd-draggable-id': string;
      style?: React.CSSProperties;
      onTransitionEnd?: React.TransitionEventHandler<any>;
    };
    dragHandleProps?: {
      'data-rbd-drag-handle-draggable-id': string;
      'data-rbd-drag-handle-context-id': string;
      'aria-describedby': string;
      'aria-labelledby': string;
      role: string;
      tabIndex: number;
      draggable: boolean;
      onDragStart: React.DragEventHandler<any>;
    } | null;
  }

  export interface DraggableStateSnapshot {
    isDragging: boolean;
    isDropAnimating: boolean;
    dropAnimation?: DropAnimation | null;
    draggingOver?: string | null;
    combineWith?: string | null;
    combineTargetFor?: string | null;
    mode?: MovementMode | null;
  }

  export interface DropAnimation {
    duration: number;
    curve: string;
    moveTo: Position;
    opacity?: number;
    scale?: number;
  }

  export interface Position {
    x: number;
    y: number;
  }

  export interface DraggableRubric {
    draggableId: string;
    type: string;
    source: DragLocation;
  }

  export class DragDropContext extends Component<DragDropContextProps> {}
  export class Droppable extends Component<DroppableProps> {}
  export class Draggable extends Component<DraggableProps> {}
}
