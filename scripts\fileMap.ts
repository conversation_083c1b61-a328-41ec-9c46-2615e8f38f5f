#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';

import { glob } from 'glob';

interface FileInfo {
  path: string;
  imports: string[];
  importedBy: string[];
}

interface FileMap {
  [filePath: string]: FileInfo;
}

// Function to extract imports from a TypeScript/TSX file
function extractImports(filePath: string, content: string): string[] {
  const imports: string[] = [];
  const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g;

  let match;
  while ((match = importRegex.exec(content)) !== null) {
    const importPath = match[1];
    if (importPath.startsWith('.')) {
      // Resolve relative imports
      const resolvedPath = path.resolve(path.dirname(filePath), importPath);
      imports.push(resolvedPath);
    } else {
      // Keep external imports as-is
      imports.push(importPath);
    }
  }

  return imports;
}

// Function to normalize file paths
function normalizePath(filePath: string): string {
  return path.resolve(filePath).replace(/\\/g, '/');
}

// Function to find TypeScript files that can be resolved from an import
function resolveImportPath(importPath: string, allFiles: string[]): string | null {
  const extensions = ['.ts', '.tsx', '.js', '.jsx'];

  for (const ext of extensions) {
    const candidate = importPath + ext;
    if (allFiles.includes(candidate)) {
      return candidate;
    }
  }

  // Check for index files
  for (const ext of extensions) {
    const indexCandidate = path.join(importPath, `index${  ext}`);
    if (allFiles.includes(indexCandidate)) {
      return indexCandidate;
    }
  }

  return null;
}

async function generateFileMap(): Promise<void> {
  try {
    console.log('🔍 Scanning for TypeScript files...');

    // Find all TypeScript and TSX files
    const tsFiles = await glob('src/**/*.{ts,tsx}', {
      ignore: ['node_modules/**', 'dist/**', 'build/**'],
      absolute: true,
    });

    const normalizedFiles = tsFiles.map(normalizePath);
    console.log(`📁 Found ${normalizedFiles.length} TypeScript files`);

    const fileMap: FileMap = {};

    // Initialize file map
    for (const filePath of normalizedFiles) {
      fileMap[filePath] = {
        path: filePath,
        imports: [],
        importedBy: [],
      };
    }

    console.log('🔗 Analyzing imports...');

    // Analyze each file for imports
    for (const filePath of normalizedFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf-8');
        const imports = extractImports(filePath, content);

        for (const importPath of imports) {
          if (!importPath.startsWith('.')) {
            // External import
            fileMap[filePath].imports.push(importPath);
            continue;
          }

          const resolvedImport = resolveImportPath(importPath, normalizedFiles);
          if (resolvedImport && fileMap[resolvedImport]) {
            fileMap[filePath].imports.push(resolvedImport);
            fileMap[resolvedImport].importedBy.push(filePath);
          }
        }
      } catch (error) {
        console.warn(`⚠️  Could not analyze ${filePath}: ${error}`);
      }
    }

    console.log('📊 Generating statistics...');

    // Generate statistics
    const stats = {
      totalFiles: normalizedFiles.length,
      filesWithImports: Object.values(fileMap).filter(f => f.imports.length > 0).length,
      filesWithImporters: Object.values(fileMap).filter(f => f.importedBy.length > 0).length,
      potentialDeadFiles: Object.values(fileMap).filter(f => f.importedBy.length === 0).length,
      heavilyImportedFiles: Object.values(fileMap)
        .filter(f => f.importedBy.length > 5)
        .sort((a, b) => b.importedBy.length - a.importedBy.length)
        .slice(0, 10),
      externalDependencies: new Set<string>(),
    };

    // Collect external dependencies
    Object.values(fileMap).forEach(file => {
      file.imports.forEach(imp => {
        if (!imp.startsWith('.') && !imp.includes('src/')) {
          stats.externalDependencies.add(imp);
        }
      });
    });

    // Create output directory
    const outputDir = 'docs/baseline';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Write detailed file map
    const outputPath = path.join(outputDir, 'file-map.json');
    fs.writeFileSync(outputPath, JSON.stringify(fileMap, null, 2));
    console.log(`✅ Detailed file map written to ${outputPath}`);

    // Write summary report
    const summaryPath = path.join(outputDir, 'file-map-summary.txt');
    const summary = `
File Map Analysis Summary
========================
Generated: ${new Date().toISOString()}

📊 STATISTICS
- Total TypeScript files: ${stats.totalFiles}
- Files with imports: ${stats.filesWithImports}
- Files that are imported: ${stats.filesWithImporters}
- Potential dead files: ${stats.potentialDeadFiles}
- External dependencies: ${stats.externalDependencies.size}

🔥 HEAVILY IMPORTED FILES (Top 10)
${stats.heavilyImportedFiles.map(f => `- ${path.relative(process.cwd(), f.path)} (${f.importedBy.length} importers)`).join('\n')}

💀 POTENTIAL DEAD FILES (Not imported by any other file)
${Object.values(fileMap)
  .filter(f => f.importedBy.length === 0)
  .map(f => `- ${path.relative(process.cwd(), f.path)}`)
  .join('\n')}

📦 EXTERNAL DEPENDENCIES
${Array.from(stats.externalDependencies).sort().map(dep => `- ${dep}`).join('\n')}
`;

    fs.writeFileSync(summaryPath, summary);
    console.log(`✅ Summary report written to ${summaryPath}`);

    console.log('\n🎉 File map generation complete!');
    console.log(`📁 ${stats.totalFiles} files analyzed`);
    console.log(`💀 ${stats.potentialDeadFiles} potential dead files found`);
    console.log(`🔥 Top imported file: ${stats.heavilyImportedFiles[0]?.path.split('/').pop()} (${stats.heavilyImportedFiles[0]?.importedBy.length} importers)`);

  } catch (error) {
    console.error('❌ Error generating file map:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
generateFileMap();
