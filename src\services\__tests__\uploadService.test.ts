/**
 * Upload Service Unit Tests
 * Tests for centralized upload functionality
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';

import { uploadService } from '../uploadService';

import type { UploadResponse } from '../uploadService';

// Mock the API
vi.mock('../api/base', () => ({
  api: {
    upload: vi.fn(),
  },
}));

describe('UploadService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('uploadVideo', () => {
    it('should upload a video file successfully', async () => {
      const { api } = await import('../api/base');
      const mockResponse: UploadResponse = {
        videoId: 'uploaded-video-id',
        status: 'success',
      };

      vi.mocked(api.upload).mockResolvedValue({ data: mockResponse, success: true });

      const mockFile = new File(['video content'], 'test-video.mp4', {
        type: 'video/mp4',
      });

      const result = await uploadService.uploadVideo(mockFile);

      expect(result).toEqual(mockResponse);
      expect(api.upload).toHaveBeenCalledWith('/api/upload/video', mockFile);
    });

    it('should throw error when upload fails', async () => {
      const { api } = await import('../api/base');

      vi.mocked(api.upload).mockRejectedValue(new Error('Upload failed'));

      const mockFile = new File(['video content'], 'test-video.mp4', {
        type: 'video/mp4',
      });

      await expect(uploadService.uploadVideo(mockFile))
        .rejects.toThrow('Upload failed');
    });

    it('should handle different file types', async () => {
      const { api } = await import('../api/base');
      const mockResponse: UploadResponse = {
        videoId: 'uploaded-video-id-2',
        status: 'processing',
      };

      vi.mocked(api.upload).mockResolvedValue({ data: mockResponse, success: true });

      const mockFile = new File(['video content'], 'test-video.avi', {
        type: 'video/avi',
      });

      const result = await uploadService.uploadVideo(mockFile);

      expect(result).toEqual(mockResponse);
      expect(api.upload).toHaveBeenCalledWith('/api/upload/video', mockFile);
    });

    it('should handle large files', async () => {
      const { api } = await import('../api/base');
      const mockResponse: UploadResponse = {
        videoId: 'large-video-id',
        status: 'uploading',
      };

      vi.mocked(api.upload).mockResolvedValue({ data: mockResponse, success: true });

      // Create a larger mock file
      const largeContent = new Array(1000).fill('a').join('');
      const mockFile = new File([largeContent], 'large-video.mp4', {
        type: 'video/mp4',
      });

      const result = await uploadService.uploadVideo(mockFile);

      expect(result).toEqual(mockResponse);
      expect(api.upload).toHaveBeenCalledWith('/api/upload/video', mockFile);
    });
  });
});
