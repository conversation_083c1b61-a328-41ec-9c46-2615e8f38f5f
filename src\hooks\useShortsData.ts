import { useCallback } from 'react';

import { useAsyncData } from '@/hooks/useAsyncData';
import { getShortsVideos } from '@/services/mockVideoService';
import type { Video } from '@/types';


/**
 * Hook for fetching shorts videos
 */
export function useShortsVideos() {
  const fetchShorts = useCallback(() => getShortsVideos(), []);
  return useAsyncData<Video[]>(fetchShorts, { initialData: [] });
}