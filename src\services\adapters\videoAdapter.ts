/**
 * Video Type Adapter
 * Converts between UnifiedVideoMetadata and Video types
 */

import type { UnifiedVideoMetadata } from '@/services/metadataNormalizationService';
import type { Video } from '@/types/video';

/**
 * Convert UnifiedVideoMetadata to Video type
 */
export function unifiedVideoToVideo(video: UnifiedVideoMetadata): Video {
  return {
    id: video.id,
    title: video.title ?? '',
    description: video.description ?? '',
    thumbnailUrl: video.thumbnailUrl ?? '',
    videoUrl: video.videoUrl ?? '',
    duration: video.duration ?? '',
    viewCount: video.views ?? 0,
    views: video.viewsFormatted ?? '',
    likeCount: video.likes ?? 0,
    likes: video.likes ?? 0,
    dislikeCount: video.dislikes ?? 0,
    dislikes: video.dislikes ?? 0,
    commentCount: video.commentCount ?? 0,
    publishedAt: video.publishedAt ?? '',
    uploadedAt: video.publishedAt ?? '',
    channelId: video.channel?.id ?? '',
    channelName: video.channel?.name ?? '',
    channelAvatarUrl: video.channel?.avatarUrl ?? '',
    channelTitle: video.channel?.name ?? '',
    category: video.category ?? '',
    tags: video.tags ?? [],
    isLive: video.isLive ?? false,
    isShort: video.isShort ?? false,
    visibility: video.visibility as any,
    createdAt: video.publishedAt ?? '',
    updatedAt: new Date().toISOString(),
    // Channel object
    channel: {
      id: video.channel?.id ?? '',
      name: video.channel?.name ?? '',
      avatarUrl: video.channel?.avatarUrl ?? '',
      isVerified: video.channel?.isVerified ?? false,
      subscribers: video.channel?.subscribers ?? 0,
      subscriberCount: video.channel?.subscribersFormatted ?? '',
    },
  } as Video;
}

/**
 * Convert array of UnifiedVideoMetadata to Video array
 */
export function unifiedVideosToVideos(videos: UnifiedVideoMetadata[]): Video[] {
  return videos.map(unifiedVideoToVideo);
}
