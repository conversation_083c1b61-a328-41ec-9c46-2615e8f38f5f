import { useCallback } from 'react';

import { useAsyncData } from '@/hooks/useAsyncData';
import type { UnifiedVideoMetadata } from '@/services/metadataNormalizationService';
import { getVideos, getShortsVideos } from '@/services/mockVideoService';
import { unifiedDataService } from '@/services/unifiedDataService';
import type { Video } from '@/types/core';


/**
 * Hook for fetching different types of videos using unified data service
 * @param type - The type of videos to fetch ('trending', 'shorts', 'all')
 * @param useUnified - Whether to use the unified data service (default: true)
 */
export function useVideosData(
  type: 'trending' | 'shorts' | 'all' = 'all',
  useUnified: boolean = true,
) {
  const fetchVideos = useCallback(async () => {
    if (useUnified) {
      // Use unified data service for normalized results
      switch (type) {
        case 'shorts': {
          const response = await unifiedDataService.getShortsVideos(30);
          return response.data;
        }
        case 'trending':
        case 'all':
        default: {
          const response = await unifiedDataService.getTrendingVideos(50);
          return response.data;
        }
      }
    } else {
      // Legacy mode - use original mock service
      switch (type) {
        case 'shorts':
          return getShortsVideos();
        case 'trending':
        case 'all':
        default:
          return getVideos();
      }
    }
  }, [type, useUnified]);

  return useAsyncData<UnifiedVideoMetadata[] | Video[]>(fetchVideos, { initialData: [] });
}

/**
 * Hook specifically for unified video data with better typing
 */
export function useUnifiedVideosData(
  type: 'trending' | 'shorts' | 'all' = 'all',
  limit: number = 50,
) {
  const fetchVideos = useCallback(async (): Promise<UnifiedVideoMetadata[]> => {
    switch (type) {
      case 'shorts': {
        const response = await unifiedDataService.getShortsVideos(limit);
        return response.data;
      }
      case 'trending':
      case 'all':
      default: {
        const response = await unifiedDataService.getTrendingVideos(limit);
        return response.data;
      }
    }
  }, [type, limit]);

  return useAsyncData<UnifiedVideoMetadata[]>(fetchVideos, { initialData: [] });
}
